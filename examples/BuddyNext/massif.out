desc: --massif-out-file=massif.out
cmd: ../../llvm/build/bin/mlir-runner -O0 -e main -entry-point-result=void -shared-libs=../../llvm/build/lib/libmlir_runner_utils.so -shared-libs=../../llvm/build/lib/libmlir_c_runner_utils.so
time_unit: i
#-----------
snapshot=0
#-----------
time=0
mem_heap_B=0
mem_heap_extra_B=0
mem_stacks_B=0
heap_tree=empty
#-----------
snapshot=1
#-----------
time=22086399
mem_heap_B=1061751
mem_heap_extra_B=78753
mem_stacks_B=0
heap_tree=empty
#-----------
snapshot=2
#-----------
time=55624822
mem_heap_B=3603483
mem_heap_extra_B=139013
mem_stacks_B=0
heap_tree=detailed
n10: 3603483 (heap allocation functions) malloc/new/new[], --alloc-fns, etc.
 n11: 1175338 0x4550A43: llvm::allocate_buffer(unsigned long, unsigned long) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
  n0: 261918 in 140 places, all below massif's threshold (1.00%)
  n1: 208896 0x4377AC6: llvm::BumpPtrAllocatorImpl<llvm::MallocAllocator, 4096ul, 4096ul, 128ul>::AllocateSlow(unsigned long, unsigned long, llvm::Align) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
   n0: 208896 in 23 places, all below massif's threshold (1.00%)
  n1: 132096 0x4B69874: mlir::StorageUniquer::getParametricStorageTypeImpl(mlir::TypeID, unsigned int, llvm::function_ref<bool (mlir::StorageUniquer::BaseStorage const*)>, llvm::function_ref<mlir::StorageUniquer::BaseStorage* (mlir::StorageUniquer::StorageAllocator&)>) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
   n0: 132096 in 25 places, all below massif's threshold (1.00%)
  n2: 109959 0x43EBEEF: llvm::ConstantDataSequential::getImpl(llvm::StringRef, llvm::Type*) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
   n1: 109695 0x43EE6B2: llvm::ConstantArray::getImpl(llvm::ArrayType*, llvm::ArrayRef<llvm::Constant*>) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
    n1: 109695 0x43F4B35: llvm::ConstantArray::get(llvm::ArrayType*, llvm::ArrayRef<llvm::Constant*>) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
     n2: 109695 0x4B42004: mlir::LLVM::detail::getLLVMConstant(llvm::Type*, mlir::Attribute, mlir::Location, mlir::LLVM::ModuleTranslation const&) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
      n2: 93294 0x4B40BA9: mlir::LLVM::detail::getLLVMConstant(llvm::Type*, mlir::Attribute, mlir::Location, mlir::LLVM::ModuleTranslation const&) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
       n1: 76851 0x4B483D2: mlir::LLVM::ModuleTranslation::convertGlobals() (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
        n1: 76851 0x4B55FCF: mlir::translateModuleToLLVMIR(mlir::Operation*, llvm::LLVMContext&, llvm::StringRef, bool) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
         n1: 76851 0x436B90C: convertMLIRModule(mlir::Operation*, llvm::LLVMContext&) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
          n1: 76851 0x436AAF8: std::unique_ptr<llvm::Module, std::default_delete<llvm::Module> > llvm::function_ref<std::unique_ptr<llvm::Module, std::default_delete<llvm::Module> > (mlir::Operation*, llvm::LLVMContext&)>::callback_fn<std::unique_ptr<llvm::Module, std::default_delete<llvm::Module> > (mlir::Operation*, llvm::LLVMContext&)>(long, mlir::Operation*, llvm::LLVMContext&) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
           n1: 76851 0x4B9EB60: mlir::ExecutionEngine::create(mlir::Operation*, mlir::ExecutionEngineOptions const&, std::unique_ptr<llvm::TargetMachine, std::default_delete<llvm::TargetMachine> >) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
            n1: 76851 0x4B6F8B5: compileAndExecute((anonymous namespace)::Options&, mlir::Operation*, llvm::StringRef, (anonymous namespace)::CompileAndExecuteConfig, void**, std::unique_ptr<llvm::TargetMachine, std::default_delete<llvm::TargetMachine> >) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
             n1: 76851 0x4B6FF5C: compileAndExecuteVoidFunction((anonymous namespace)::Options&, mlir::Operation*, llvm::StringRef, (anonymous namespace)::CompileAndExecuteConfig, std::unique_ptr<llvm::TargetMachine, std::default_delete<llvm::TargetMachine> >) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
              n1: 76851 0x4B715A2: mlir::JitRunnerMain(int, char**, mlir::DialectRegistry const&, mlir::JitRunnerConfig) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
               n0: 76851 0x42B4749: main (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
       n0: 16443 in 1 place, below massif's threshold (1.00%)
      n0: 16401 in 1 place, below massif's threshold (1.00%)
   n0: 264 in 1 place, below massif's threshold (1.00%)
  n1: 107295 0x5177EAC: llvm::orc::getELFObjectFileSymbolInfo(llvm::orc::ExecutionSession&, llvm::object::ELFObjectFileBase const&) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
   n1: 107295 0x5179875: llvm::orc::getObjectFileInterface(llvm::orc::ExecutionSession&, llvm::MemoryBufferRef) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
    n1: 107295 0x5180A71: llvm::orc::ObjectLayer::add(llvm::orc::JITDylib&, std::unique_ptr<llvm::MemoryBuffer, std::default_delete<llvm::MemoryBuffer> >) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
     n1: 107295 0x4B9D938: mlir::ExecutionEngine::create(mlir::Operation*, mlir::ExecutionEngineOptions const&, std::unique_ptr<llvm::TargetMachine, std::default_delete<llvm::TargetMachine> >)::{lambda(llvm::orc::ExecutionSession&, llvm::Triple const&)#2}::operator()(llvm::orc::ExecutionSession&, llvm::Triple const&) const [clone .constprop.0] (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
      n1: 107295 0x4B9E087: std::_Function_handler<llvm::Expected<std::unique_ptr<llvm::orc::ObjectLayer, std::default_delete<llvm::orc::ObjectLayer> > > (llvm::orc::ExecutionSession&, llvm::Triple const&), mlir::ExecutionEngine::create(mlir::Operation*, mlir::ExecutionEngineOptions const&, std::unique_ptr<llvm::TargetMachine, std::default_delete<llvm::TargetMachine> >)::{lambda(llvm::orc::ExecutionSession&, llvm::Triple const&)#2}>::_M_invoke(std::_Any_data const&, llvm::orc::ExecutionSession&, llvm::Triple const&) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
       n1: 107295 0x5187AB0: llvm::orc::LLJIT::createObjectLinkingLayer(llvm::orc::LLJITBuilderState&, llvm::orc::ExecutionSession&) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
        n1: 107295 0x518D2D2: llvm::orc::LLJIT::LLJIT(llvm::orc::LLJITBuilderState&, llvm::Error&) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
         n1: 107295 0x4B9FB34: mlir::ExecutionEngine::create(mlir::Operation*, mlir::ExecutionEngineOptions const&, std::unique_ptr<llvm::TargetMachine, std::default_delete<llvm::TargetMachine> >) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
          n1: 107295 0x4B6F8B5: compileAndExecute((anonymous namespace)::Options&, mlir::Operation*, llvm::StringRef, (anonymous namespace)::CompileAndExecuteConfig, void**, std::unique_ptr<llvm::TargetMachine, std::default_delete<llvm::TargetMachine> >) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
           n1: 107295 0x4B6FF5C: compileAndExecuteVoidFunction((anonymous namespace)::Options&, mlir::Operation*, llvm::StringRef, (anonymous namespace)::CompileAndExecuteConfig, std::unique_ptr<llvm::TargetMachine, std::default_delete<llvm::TargetMachine> >) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
            n1: 107295 0x4B715A2: mlir::JitRunnerMain(int, char**, mlir::DialectRegistry const&, mlir::JitRunnerConfig) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
             n0: 107295 0x42B4749: main (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
  n1: 70656 0x4B68B07: mlir::StorageUniquer::getParametricStorageTypeImpl(mlir::TypeID, unsigned int, llvm::function_ref<bool (mlir::StorageUniquer::BaseStorage const*)>, llvm::function_ref<mlir::StorageUniquer::BaseStorage* (mlir::StorageUniquer::StorageAllocator&)>) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
   n0: 70656 in 22 places, all below massif's threshold (1.00%)
  n2: 70502 0x452AAFF: std::pair<llvm::StringMapIterator<llvm::cl::Option*>, bool> llvm::StringMap<llvm::cl::Option*, llvm::MallocAllocator>::try_emplace_with_hash<llvm::cl::Option*>(llvm::StringRef, unsigned int, llvm::cl::Option*&&) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
   n2: 70430 0x452C0B4: (anonymous namespace)::CommandLineParser::addOption(llvm::cl::Option*, llvm::cl::SubCommand*) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
    n1: 70222 0x452C945: llvm::cl::Option::addArgument() (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
     n0: 70222 in 1695 places, all below massif's threshold (1.00%)
    n0: 208 in 2 places, all below massif's threshold (1.00%)
   n0: 72 in 1 place, below massif's threshold (1.00%)
  n1: 58368 0x5154254: llvm::DenseMap<llvm::orc::SymbolStringPtr, std::shared_ptr<llvm::orc::JITDylib::UnmaterializedInfo>, llvm::DenseMapInfo<llvm::orc::SymbolStringPtr, void>, llvm::detail::DenseMapPair<llvm::orc::SymbolStringPtr, std::shared_ptr<llvm::orc::JITDylib::UnmaterializedInfo> > >::grow(unsigned int) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
   n2: 58368 0x5156544: llvm::orc::JITDylib::installMaterializationUnit(std::unique_ptr<llvm::orc::MaterializationUnit, std::default_delete<llvm::orc::MaterializationUnit> >, llvm::orc::ResourceTracker&) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
    n1: 55296 0x517EBB1: llvm::Error llvm::orc::JITDylib::define<llvm::orc::BasicObjectLayerMaterializationUnit>(std::unique_ptr<llvm::orc::BasicObjectLayerMaterializationUnit, std::default_delete<llvm::orc::BasicObjectLayerMaterializationUnit> >&&, llvm::IntrusiveRefCntPtr<llvm::orc::ResourceTracker>) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
     n1: 55296 0x517F38E: llvm::orc::ObjectLayer::add(llvm::IntrusiveRefCntPtr<llvm::orc::ResourceTracker>, std::unique_ptr<llvm::MemoryBuffer, std::default_delete<llvm::MemoryBuffer> >, llvm::orc::MaterializationUnit::Interface) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
      n1: 55296 0x5180BA2: llvm::orc::ObjectLayer::add(llvm::orc::JITDylib&, std::unique_ptr<llvm::MemoryBuffer, std::default_delete<llvm::MemoryBuffer> >) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
       n1: 55296 0x4B9D938: mlir::ExecutionEngine::create(mlir::Operation*, mlir::ExecutionEngineOptions const&, std::unique_ptr<llvm::TargetMachine, std::default_delete<llvm::TargetMachine> >)::{lambda(llvm::orc::ExecutionSession&, llvm::Triple const&)#2}::operator()(llvm::orc::ExecutionSession&, llvm::Triple const&) const [clone .constprop.0] (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
        n1: 55296 0x4B9E087: std::_Function_handler<llvm::Expected<std::unique_ptr<llvm::orc::ObjectLayer, std::default_delete<llvm::orc::ObjectLayer> > > (llvm::orc::ExecutionSession&, llvm::Triple const&), mlir::ExecutionEngine::create(mlir::Operation*, mlir::ExecutionEngineOptions const&, std::unique_ptr<llvm::TargetMachine, std::default_delete<llvm::TargetMachine> >)::{lambda(llvm::orc::ExecutionSession&, llvm::Triple const&)#2}>::_M_invoke(std::_Any_data const&, llvm::orc::ExecutionSession&, llvm::Triple const&) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
         n1: 55296 0x5187AB0: llvm::orc::LLJIT::createObjectLinkingLayer(llvm::orc::LLJITBuilderState&, llvm::orc::ExecutionSession&) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
          n1: 55296 0x518D2D2: llvm::orc::LLJIT::LLJIT(llvm::orc::LLJITBuilderState&, llvm::Error&) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
           n1: 55296 0x4B9FB34: mlir::ExecutionEngine::create(mlir::Operation*, mlir::ExecutionEngineOptions const&, std::unique_ptr<llvm::TargetMachine, std::default_delete<llvm::TargetMachine> >) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
            n1: 55296 0x4B6F8B5: compileAndExecute((anonymous namespace)::Options&, mlir::Operation*, llvm::StringRef, (anonymous namespace)::CompileAndExecuteConfig, void**, std::unique_ptr<llvm::TargetMachine, std::default_delete<llvm::TargetMachine> >) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
             n1: 55296 0x4B6FF5C: compileAndExecuteVoidFunction((anonymous namespace)::Options&, mlir::Operation*, llvm::StringRef, (anonymous namespace)::CompileAndExecuteConfig, std::unique_ptr<llvm::TargetMachine, std::default_delete<llvm::TargetMachine> >) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
              n1: 55296 0x4B715A2: mlir::JitRunnerMain(int, char**, mlir::DialectRegistry const&, mlir::JitRunnerConfig) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
               n0: 55296 0x42B4749: main (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
    n0: 3072 in 1 place, below massif's threshold (1.00%)
  n1: 58368 0x5154664: llvm::DenseMap<llvm::orc::SymbolStringPtr, llvm::orc::JITDylib::SymbolTableEntry, llvm::DenseMapInfo<llvm::orc::SymbolStringPtr, void>, llvm::detail::DenseMapPair<llvm::orc::SymbolStringPtr, llvm::orc::JITDylib::SymbolTableEntry> >::grow(unsigned int) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
   n1: 58368 0x5154932: llvm::detail::DenseMapPair<llvm::orc::SymbolStringPtr, llvm::orc::JITDylib::SymbolTableEntry>* llvm::DenseMapBase<llvm::DenseMap<llvm::orc::SymbolStringPtr, llvm::orc::JITDylib::SymbolTableEntry, llvm::DenseMapInfo<llvm::orc::SymbolStringPtr, void>, llvm::detail::DenseMapPair<llvm::orc::SymbolStringPtr, llvm::orc::JITDylib::SymbolTableEntry> >, llvm::orc::SymbolStringPtr, llvm::orc::JITDylib::SymbolTableEntry, llvm::DenseMapInfo<llvm::orc::SymbolStringPtr, void>, llvm::detail::DenseMapPair<llvm::orc::SymbolStringPtr, llvm::orc::JITDylib::SymbolTableEntry> >::InsertIntoBucketImpl<llvm::orc::SymbolStringPtr>(llvm::orc::SymbolStringPtr const&, llvm::detail::DenseMapPair<llvm::orc::SymbolStringPtr, llvm::orc::JITDylib::SymbolTableEntry>*) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
    n2: 58368 0x51677D9: llvm::orc::JITDylib::defineImpl(llvm::orc::MaterializationUnit&) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
     n1: 55296 0x517EB41: llvm::Error llvm::orc::JITDylib::define<llvm::orc::BasicObjectLayerMaterializationUnit>(std::unique_ptr<llvm::orc::BasicObjectLayerMaterializationUnit, std::default_delete<llvm::orc::BasicObjectLayerMaterializationUnit> >&&, llvm::IntrusiveRefCntPtr<llvm::orc::ResourceTracker>) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
      n1: 55296 0x517F38E: llvm::orc::ObjectLayer::add(llvm::IntrusiveRefCntPtr<llvm::orc::ResourceTracker>, std::unique_ptr<llvm::MemoryBuffer, std::default_delete<llvm::MemoryBuffer> >, llvm::orc::MaterializationUnit::Interface) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
       n1: 55296 0x5180BA2: llvm::orc::ObjectLayer::add(llvm::orc::JITDylib&, std::unique_ptr<llvm::MemoryBuffer, std::default_delete<llvm::MemoryBuffer> >) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
        n1: 55296 0x4B9D938: mlir::ExecutionEngine::create(mlir::Operation*, mlir::ExecutionEngineOptions const&, std::unique_ptr<llvm::TargetMachine, std::default_delete<llvm::TargetMachine> >)::{lambda(llvm::orc::ExecutionSession&, llvm::Triple const&)#2}::operator()(llvm::orc::ExecutionSession&, llvm::Triple const&) const [clone .constprop.0] (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
         n1: 55296 0x4B9E087: std::_Function_handler<llvm::Expected<std::unique_ptr<llvm::orc::ObjectLayer, std::default_delete<llvm::orc::ObjectLayer> > > (llvm::orc::ExecutionSession&, llvm::Triple const&), mlir::ExecutionEngine::create(mlir::Operation*, mlir::ExecutionEngineOptions const&, std::unique_ptr<llvm::TargetMachine, std::default_delete<llvm::TargetMachine> >)::{lambda(llvm::orc::ExecutionSession&, llvm::Triple const&)#2}>::_M_invoke(std::_Any_data const&, llvm::orc::ExecutionSession&, llvm::Triple const&) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
          n1: 55296 0x5187AB0: llvm::orc::LLJIT::createObjectLinkingLayer(llvm::orc::LLJITBuilderState&, llvm::orc::ExecutionSession&) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
           n1: 55296 0x518D2D2: llvm::orc::LLJIT::LLJIT(llvm::orc::LLJITBuilderState&, llvm::Error&) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
            n1: 55296 0x4B9FB34: mlir::ExecutionEngine::create(mlir::Operation*, mlir::ExecutionEngineOptions const&, std::unique_ptr<llvm::TargetMachine, std::default_delete<llvm::TargetMachine> >) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
             n1: 55296 0x4B6F8B5: compileAndExecute((anonymous namespace)::Options&, mlir::Operation*, llvm::StringRef, (anonymous namespace)::CompileAndExecuteConfig, void**, std::unique_ptr<llvm::TargetMachine, std::default_delete<llvm::TargetMachine> >) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
              n1: 55296 0x4B6FF5C: compileAndExecuteVoidFunction((anonymous namespace)::Options&, mlir::Operation*, llvm::StringRef, (anonymous namespace)::CompileAndExecuteConfig, std::unique_ptr<llvm::TargetMachine, std::default_delete<llvm::TargetMachine> >) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
               n1: 55296 0x4B715A2: mlir::JitRunnerMain(int, char**, mlir::DialectRegistry const&, mlir::JitRunnerConfig) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
                n0: 55296 0x42B4749: main (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
     n0: 3072 in 1 place, below massif's threshold (1.00%)
  n1: 53248 0x780CD68: llvm::DenseMap<llvm::Pass*, llvm::SmallPtrSet<llvm::Pass*, 8u>, llvm::DenseMapInfo<llvm::Pass*, void>, llvm::detail::DenseMapPair<llvm::Pass*, llvm::SmallPtrSet<llvm::Pass*, 8u> > >::grow(unsigned int) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
   n1: 53248 0x7812A68: llvm::PMTopLevelManager::setLastUser(llvm::ArrayRef<llvm::Pass*>, llvm::Pass*) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
    n1: 53248 0x7813D55: llvm::PMDataManager::add(llvm::Pass*, bool) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
     n2: 53248 0x7811BD4: llvm::PMTopLevelManager::schedulePass(llvm::Pass*) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
      n1: 53248 0x5FA91A0: llvm::TargetPassConfig::addPass(llvm::Pass*) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
       n1: 53248 0x45BD0D7: (anonymous namespace)::X86PassConfig::addPreEmitPass() (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
        n1: 53248 0x5FABEE4: llvm::TargetPassConfig::addMachinePasses() (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
         n1: 53248 0x5D0A872: addPassesToGenerateCode(llvm::CodeGenTargetMachineImpl&, llvm::legacy::PassManagerBase&, bool, llvm::MachineModuleInfoWrapperPass&) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
          n1: 53248 0x5D0AA1A: llvm::CodeGenTargetMachineImpl::addPassesToEmitMC(llvm::legacy::PassManagerBase&, llvm::MCContext*&, llvm::raw_pwrite_stream&, bool) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
           n1: 53248 0x5144AF2: llvm::orc::SimpleCompiler::operator()(llvm::Module&) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
            n1: 53248 0x517AD3C: llvm::orc::IRCompileLayer::emit(std::unique_ptr<llvm::orc::MaterializationResponsibility, std::default_delete<llvm::orc::MaterializationResponsibility> >, llvm::orc::ThreadSafeModule) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
             n1: 53248 0x51B0088: llvm::orc::IRTransformLayer::emit(std::unique_ptr<llvm::orc::MaterializationResponsibility, std::default_delete<llvm::orc::MaterializationResponsibility> >, llvm::orc::ThreadSafeModule) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
              n1: 53248 0x51B0088: llvm::orc::IRTransformLayer::emit(std::unique_ptr<llvm::orc::MaterializationResponsibility, std::default_delete<llvm::orc::MaterializationResponsibility> >, llvm::orc::ThreadSafeModule) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
               n1: 53248 0x517FFB5: llvm::orc::BasicIRLayerMaterializationUnit::materialize(std::unique_ptr<llvm::orc::MaterializationResponsibility, std::default_delete<llvm::orc::MaterializationResponsibility> >) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
                n1: 53248 0x516363F: llvm::orc::MaterializationTask::run() (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
                 n1: 53248 0x514864C: llvm::orc::ExecutionSession::dispatchTask(std::unique_ptr<llvm::orc::Task, std::default_delete<llvm::orc::Task> >) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
                  n1: 53248 0x516399B: llvm::orc::ExecutionSession::dispatchOutstandingMUs() (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
                   n1: 53248 0x5164EA4: llvm::orc::ExecutionSession::OL_completeLookup(std::unique_ptr<llvm::orc::InProgressLookupState, std::default_delete<llvm::orc::InProgressLookupState> >, std::shared_ptr<llvm::orc::AsynchronousSymbolQuery>, std::function<void (llvm::DenseMap<llvm::orc::JITDylib*, llvm::DenseSet<llvm::orc::SymbolStringPtr, llvm::DenseMapInfo<llvm::orc::SymbolStringPtr, void> >, llvm::DenseMapInfo<llvm::orc::JITDylib*, void>, llvm::detail::DenseMapPair<llvm::orc::JITDylib*, llvm::DenseSet<llvm::orc::SymbolStringPtr, llvm::DenseMapInfo<llvm::orc::SymbolStringPtr, void> > > > const&)>) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
                    n1: 53248 0x5165C73: llvm::orc::InProgressFullLookupState::complete(std::unique_ptr<llvm::orc::InProgressLookupState, std::default_delete<llvm::orc::InProgressLookupState> >) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
                     n1: 53248 0x515A75E: llvm::orc::ExecutionSession::OL_applyQueryPhase1(std::unique_ptr<llvm::orc::InProgressLookupState, std::default_delete<llvm::orc::InProgressLookupState> >, llvm::Error) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
                      n1: 53248 0x5166052: llvm::orc::ExecutionSession::lookup(llvm::orc::LookupKind, std::vector<std::pair<llvm::orc::JITDylib*, llvm::orc::JITDylibLookupFlags>, std::allocator<std::pair<llvm::orc::JITDylib*, llvm::orc::JITDylibLookupFlags> > > const&, llvm::orc::SymbolLookupSet, llvm::orc::SymbolState, llvm::unique_function<void (llvm::Expected<llvm::DenseMap<llvm::orc::SymbolStringPtr, llvm::orc::ExecutorSymbolDef, llvm::DenseMapInfo<llvm::orc::SymbolStringPtr, void>, llvm::detail::DenseMapPair<llvm::orc::SymbolStringPtr, llvm::orc::ExecutorSymbolDef> > >)>, std::function<void (llvm::DenseMap<llvm::orc::JITDylib*, llvm::DenseSet<llvm::orc::SymbolStringPtr, llvm::DenseMapInfo<llvm::orc::SymbolStringPtr, void> >, llvm::DenseMapInfo<llvm::orc::JITDylib*, void>, llvm::detail::DenseMapPair<llvm::orc::JITDylib*, llvm::DenseSet<llvm::orc::SymbolStringPtr, llvm::DenseMapInfo<llvm::orc::SymbolStringPtr, void> > > > const&)>) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
                       n1: 53248 0x5166327: llvm::orc::ExecutionSession::lookup(std::vector<std::pair<llvm::orc::JITDylib*, llvm::orc::JITDylibLookupFlags>, std::allocator<std::pair<llvm::orc::JITDylib*, llvm::orc::JITDylibLookupFlags> > > const&, llvm::orc::SymbolLookupSet, llvm::orc::LookupKind, llvm::orc::SymbolState, std::function<void (llvm::DenseMap<llvm::orc::JITDylib*, llvm::DenseSet<llvm::orc::SymbolStringPtr, llvm::DenseMapInfo<llvm::orc::SymbolStringPtr, void> >, llvm::DenseMapInfo<llvm::orc::JITDylib*, void>, llvm::detail::DenseMapPair<llvm::orc::JITDylib*, llvm::DenseSet<llvm::orc::SymbolStringPtr, llvm::DenseMapInfo<llvm::orc::SymbolStringPtr, void> > > > const&)>) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
                        n1: 53248 0x516671D: llvm::orc::ExecutionSession::lookup(std::vector<std::pair<llvm::orc::JITDylib*, llvm::orc::JITDylibLookupFlags>, std::allocator<std::pair<llvm::orc::JITDylib*, llvm::orc::JITDylibLookupFlags> > > const&, llvm::orc::SymbolStringPtr, llvm::orc::SymbolState) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
                         n1: 53248 0x5189AB9: llvm::orc::LLJIT::lookupLinkerMangled(llvm::orc::JITDylib&, llvm::orc::SymbolStringPtr) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
                          n1: 53248 0x4B9C824: mlir::ExecutionEngine::lookup(llvm::StringRef) const (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
                           n1: 53248 0x4B9CF78: mlir::ExecutionEngine::lookupPacked(llvm::StringRef) const (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
                            n1: 53248 0x4B6F974: compileAndExecute((anonymous namespace)::Options&, mlir::Operation*, llvm::StringRef, (anonymous namespace)::CompileAndExecuteConfig, void**, std::unique_ptr<llvm::TargetMachine, std::default_delete<llvm::TargetMachine> >) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
                             n1: 53248 0x4B6FF5C: compileAndExecuteVoidFunction((anonymous namespace)::Options&, mlir::Operation*, llvm::StringRef, (anonymous namespace)::CompileAndExecuteConfig, std::unique_ptr<llvm::TargetMachine, std::default_delete<llvm::TargetMachine> >) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
                              n0: 53248 0x4B715A2: mlir::JitRunnerMain(int, char**, mlir::DialectRegistry const&, mlir::JitRunnerConfig) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
      n0: 0 in 2 places, all below massif's threshold (1.00%)
  n1: 44032 0x514335A: llvm::DenseMap<llvm::orc::SymbolStringPtr, llvm::JITSymbolFlags, llvm::DenseMapInfo<llvm::orc::SymbolStringPtr, void>, llvm::detail::DenseMapPair<llvm::orc::SymbolStringPtr, llvm::JITSymbolFlags> >::grow(unsigned int) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
   n0: 44032 in 4 places, all below massif's threshold (1.00%)
 n2: 954584 0x4383EBE: llvm::User::operator new(unsigned long, llvm::User::IntrusiveOperandsAllocMarker) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
  n2: 839144 0x43F4CC2: llvm::ConstantArray::get(llvm::ArrayType*, llvm::ArrayRef<llvm::Constant*>) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
   n2: 838488 0x4B42004: mlir::LLVM::detail::getLLVMConstant(llvm::Type*, mlir::Attribute, mlir::Location, mlir::LLVM::ModuleTranslation const&) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
    n1: 835824 0x4B483D2: mlir::LLVM::ModuleTranslation::convertGlobals() (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
     n1: 835824 0x4B55FCF: mlir::translateModuleToLLVMIR(mlir::Operation*, llvm::LLVMContext&, llvm::StringRef, bool) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
      n1: 835824 0x436B90C: convertMLIRModule(mlir::Operation*, llvm::LLVMContext&) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
       n1: 835824 0x436AAF8: std::unique_ptr<llvm::Module, std::default_delete<llvm::Module> > llvm::function_ref<std::unique_ptr<llvm::Module, std::default_delete<llvm::Module> > (mlir::Operation*, llvm::LLVMContext&)>::callback_fn<std::unique_ptr<llvm::Module, std::default_delete<llvm::Module> > (mlir::Operation*, llvm::LLVMContext&)>(long, mlir::Operation*, llvm::LLVMContext&) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
        n1: 835824 0x4B9EB60: mlir::ExecutionEngine::create(mlir::Operation*, mlir::ExecutionEngineOptions const&, std::unique_ptr<llvm::TargetMachine, std::default_delete<llvm::TargetMachine> >) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
         n1: 835824 0x4B6F8B5: compileAndExecute((anonymous namespace)::Options&, mlir::Operation*, llvm::StringRef, (anonymous namespace)::CompileAndExecuteConfig, void**, std::unique_ptr<llvm::TargetMachine, std::default_delete<llvm::TargetMachine> >) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
          n1: 835824 0x4B6FF5C: compileAndExecuteVoidFunction((anonymous namespace)::Options&, mlir::Operation*, llvm::StringRef, (anonymous namespace)::CompileAndExecuteConfig, std::unique_ptr<llvm::TargetMachine, std::default_delete<llvm::TargetMachine> >) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
           n1: 835824 0x4B715A2: mlir::JitRunnerMain(int, char**, mlir::DialectRegistry const&, mlir::JitRunnerConfig) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
            n0: 835824 0x42B4749: main (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
    n0: 2664 in 1 place, below massif's threshold (1.00%)
   n0: 656 in 1 place, below massif's threshold (1.00%)
  n0: 115440 in 36 places, all below massif's threshold (1.00%)
 n0: 475697 in 1990 places, all below massif's threshold (1.00%)
 n1: 413592 0x45C74E1: llvm::X86TargetMachine::getSubtargetImpl(llvm::Function const&) const (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
  n1: 413592 0x45C7822: llvm::X86TargetMachine::getTargetTransformInfo(llvm::Function const&) const (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
   n1: 413592 0x620EE97: std::_Function_handler<llvm::TargetTransformInfo (llvm::Function const&), llvm::TargetMachine::getTargetIRAnalysis() const::{lambda(llvm::Function const&)#1}>::_M_invoke(std::_Any_data const&, llvm::Function const&) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
    n1: 413592 0x754A4F9: llvm::TargetTransformInfoWrapperPass::getTTI(llvm::Function const&) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
     n1: 413592 0x5E690A6: (anonymous namespace)::PreISelIntrinsicLowering::expandMemIntrinsicUses(llvm::Function&) const (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
      n1: 413592 0x5E6C9DC: (anonymous namespace)::PreISelIntrinsicLowering::lowerIntrinsics(llvm::Module&) const [clone .constprop.0] (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
       n1: 413592 0x5E6CDA9: (anonymous namespace)::PreISelIntrinsicLoweringLegacyPass::runOnModule(llvm::Module&) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
        n1: 413592 0x7811088: llvm::legacy::PassManagerImpl::run(llvm::Module&) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
         n1: 413592 0x5144B05: llvm::orc::SimpleCompiler::operator()(llvm::Module&) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
          n1: 413592 0x517AD3C: llvm::orc::IRCompileLayer::emit(std::unique_ptr<llvm::orc::MaterializationResponsibility, std::default_delete<llvm::orc::MaterializationResponsibility> >, llvm::orc::ThreadSafeModule) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
           n1: 413592 0x51B0088: llvm::orc::IRTransformLayer::emit(std::unique_ptr<llvm::orc::MaterializationResponsibility, std::default_delete<llvm::orc::MaterializationResponsibility> >, llvm::orc::ThreadSafeModule) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
            n1: 413592 0x51B0088: llvm::orc::IRTransformLayer::emit(std::unique_ptr<llvm::orc::MaterializationResponsibility, std::default_delete<llvm::orc::MaterializationResponsibility> >, llvm::orc::ThreadSafeModule) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
             n1: 413592 0x517FFB5: llvm::orc::BasicIRLayerMaterializationUnit::materialize(std::unique_ptr<llvm::orc::MaterializationResponsibility, std::default_delete<llvm::orc::MaterializationResponsibility> >) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
              n1: 413592 0x516363F: llvm::orc::MaterializationTask::run() (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
               n1: 413592 0x514864C: llvm::orc::ExecutionSession::dispatchTask(std::unique_ptr<llvm::orc::Task, std::default_delete<llvm::orc::Task> >) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
                n1: 413592 0x516399B: llvm::orc::ExecutionSession::dispatchOutstandingMUs() (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
                 n1: 413592 0x5164EA4: llvm::orc::ExecutionSession::OL_completeLookup(std::unique_ptr<llvm::orc::InProgressLookupState, std::default_delete<llvm::orc::InProgressLookupState> >, std::shared_ptr<llvm::orc::AsynchronousSymbolQuery>, std::function<void (llvm::DenseMap<llvm::orc::JITDylib*, llvm::DenseSet<llvm::orc::SymbolStringPtr, llvm::DenseMapInfo<llvm::orc::SymbolStringPtr, void> >, llvm::DenseMapInfo<llvm::orc::JITDylib*, void>, llvm::detail::DenseMapPair<llvm::orc::JITDylib*, llvm::DenseSet<llvm::orc::SymbolStringPtr, llvm::DenseMapInfo<llvm::orc::SymbolStringPtr, void> > > > const&)>) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
                  n1: 413592 0x5165C73: llvm::orc::InProgressFullLookupState::complete(std::unique_ptr<llvm::orc::InProgressLookupState, std::default_delete<llvm::orc::InProgressLookupState> >) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
                   n1: 413592 0x515A75E: llvm::orc::ExecutionSession::OL_applyQueryPhase1(std::unique_ptr<llvm::orc::InProgressLookupState, std::default_delete<llvm::orc::InProgressLookupState> >, llvm::Error) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
                    n1: 413592 0x5166052: llvm::orc::ExecutionSession::lookup(llvm::orc::LookupKind, std::vector<std::pair<llvm::orc::JITDylib*, llvm::orc::JITDylibLookupFlags>, std::allocator<std::pair<llvm::orc::JITDylib*, llvm::orc::JITDylibLookupFlags> > > const&, llvm::orc::SymbolLookupSet, llvm::orc::SymbolState, llvm::unique_function<void (llvm::Expected<llvm::DenseMap<llvm::orc::SymbolStringPtr, llvm::orc::ExecutorSymbolDef, llvm::DenseMapInfo<llvm::orc::SymbolStringPtr, void>, llvm::detail::DenseMapPair<llvm::orc::SymbolStringPtr, llvm::orc::ExecutorSymbolDef> > >)>, std::function<void (llvm::DenseMap<llvm::orc::JITDylib*, llvm::DenseSet<llvm::orc::SymbolStringPtr, llvm::DenseMapInfo<llvm::orc::SymbolStringPtr, void> >, llvm::DenseMapInfo<llvm::orc::JITDylib*, void>, llvm::detail::DenseMapPair<llvm::orc::JITDylib*, llvm::DenseSet<llvm::orc::SymbolStringPtr, llvm::DenseMapInfo<llvm::orc::SymbolStringPtr, void> > > > const&)>) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
                     n1: 413592 0x5166327: llvm::orc::ExecutionSession::lookup(std::vector<std::pair<llvm::orc::JITDylib*, llvm::orc::JITDylibLookupFlags>, std::allocator<std::pair<llvm::orc::JITDylib*, llvm::orc::JITDylibLookupFlags> > > const&, llvm::orc::SymbolLookupSet, llvm::orc::LookupKind, llvm::orc::SymbolState, std::function<void (llvm::DenseMap<llvm::orc::JITDylib*, llvm::DenseSet<llvm::orc::SymbolStringPtr, llvm::DenseMapInfo<llvm::orc::SymbolStringPtr, void> >, llvm::DenseMapInfo<llvm::orc::JITDylib*, void>, llvm::detail::DenseMapPair<llvm::orc::JITDylib*, llvm::DenseSet<llvm::orc::SymbolStringPtr, llvm::DenseMapInfo<llvm::orc::SymbolStringPtr, void> > > > const&)>) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
                      n1: 413592 0x516671D: llvm::orc::ExecutionSession::lookup(std::vector<std::pair<llvm::orc::JITDylib*, llvm::orc::JITDylibLookupFlags>, std::allocator<std::pair<llvm::orc::JITDylib*, llvm::orc::JITDylibLookupFlags> > > const&, llvm::orc::SymbolStringPtr, llvm::orc::SymbolState) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
                       n1: 413592 0x5189AB9: llvm::orc::LLJIT::lookupLinkerMangled(llvm::orc::JITDylib&, llvm::orc::SymbolStringPtr) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
                        n1: 413592 0x4B9C824: mlir::ExecutionEngine::lookup(llvm::StringRef) const (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
                         n1: 413592 0x4B9CF78: mlir::ExecutionEngine::lookupPacked(llvm::StringRef) const (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
                          n1: 413592 0x4B6F974: compileAndExecute((anonymous namespace)::Options&, mlir::Operation*, llvm::StringRef, (anonymous namespace)::CompileAndExecuteConfig, void**, std::unique_ptr<llvm::TargetMachine, std::default_delete<llvm::TargetMachine> >) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
                           n1: 413592 0x4B6FF5C: compileAndExecuteVoidFunction((anonymous namespace)::Options&, mlir::Operation*, llvm::StringRef, (anonymous namespace)::CompileAndExecuteConfig, std::unique_ptr<llvm::TargetMachine, std::default_delete<llvm::TargetMachine> >) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
                            n1: 413592 0x4B715A2: mlir::JitRunnerMain(int, char**, mlir::DialectRegistry const&, mlir::JitRunnerConfig) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
                             n0: 413592 0x42B4749: main (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
 n1: 150088 0x4AB6764: mlir::Operation::create(mlir::Location, mlir::OperationName, mlir::TypeRange, mlir::ValueRange, mlir::DictionaryAttr, mlir::OpaqueProperties, mlir::BlockRange, unsigned int) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
  n1: 150088 0x4AB7B01: mlir::Operation::create(mlir::Location, mlir::OperationName, mlir::TypeRange, mlir::ValueRange, mlir::NamedAttrList&&, mlir::OpaqueProperties, mlir::BlockRange, mlir::RegionRange) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
   n1: 150088 0x4AB7EAD: mlir::Operation::create(mlir::OperationState const&) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
    n2: 150088 0x4B03AB2: mlir::OpBuilder::create(mlir::OperationState const&) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
     n2: 149032 0x5B1049F: (anonymous namespace)::OperationParser::parseOperation() (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
      n1: 123200 0x5B12DCC: (anonymous namespace)::OperationParser::parseBlock(mlir::Block*&) [clone .part.0] (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
       n1: 123200 0x5B13315: (anonymous namespace)::OperationParser::parseRegionBody(mlir::Region&, llvm::SMLoc, llvm::ArrayRef<mlir::OpAsmParser::Argument>, bool) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
        n1: 123200 0x5B139E1: (anonymous namespace)::OperationParser::parseRegion(mlir::Region&, llvm::ArrayRef<mlir::OpAsmParser::Argument>, bool) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
         n1: 123200 0x5B150B2: (anonymous namespace)::CustomOpAsmParser::parseOptionalRegion(mlir::Region&, llvm::ArrayRef<mlir::OpAsmParser::Argument>, bool) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
          n1: 123200 0x7170E1D: mlir::LLVM::LLVMFuncOp::parse(mlir::OpAsmParser&, mlir::OperationState&) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
           n1: 123200 0x5B0FDA9: (anonymous namespace)::OperationParser::parseOperation() (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
            n1: 123200 0x5B131FF: (anonymous namespace)::OperationParser::parseRegionBody(mlir::Region&, llvm::SMLoc, llvm::ArrayRef<mlir::OpAsmParser::Argument>, bool) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
             n1: 123200 0x5B152BA: (anonymous namespace)::CustomOpAsmParser::parseRegion(mlir::Region&, llvm::ArrayRef<mlir::OpAsmParser::Argument>, bool) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
              n1: 123200 0x4A5DF1C: mlir::ModuleOp::parse(mlir::OpAsmParser&, mlir::OperationState&) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
               n1: 123200 0x5B0FDA9: (anonymous namespace)::OperationParser::parseOperation() (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
                n1: 123200 0x5B11687: mlir::parseAsmSourceFile(llvm::SourceMgr const&, mlir::Block*, mlir::ParserConfig const&, mlir::AsmParserState*, mlir::AsmParserCodeCompleteContext*) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
                 n1: 123200 0x4B6E831: parseMLIRInput(llvm::StringRef, bool, mlir::MLIRContext*) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
                  n1: 123200 0x4B712A1: mlir::JitRunnerMain(int, char**, mlir::DialectRegistry const&, mlir::JitRunnerConfig) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
                   n0: 123200 0x42B4749: main (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
      n0: 25832 in 2 places, all below massif's threshold (1.00%)
     n0: 1056 in 2 places, all below massif's threshold (1.00%)
 n1: 131584 0x493B37D: llvm::X86Subtarget::X86Subtarget(llvm::Triple const&, llvm::StringRef, llvm::StringRef, llvm::StringRef, llvm::X86TargetMachine const&, llvm::MaybeAlign, unsigned int, unsigned int) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
  n1: 131584 0x45C7521: llvm::X86TargetMachine::getSubtargetImpl(llvm::Function const&) const (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
   n1: 131584 0x45C7822: llvm::X86TargetMachine::getTargetTransformInfo(llvm::Function const&) const (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
    n1: 131584 0x620EE97: std::_Function_handler<llvm::TargetTransformInfo (llvm::Function const&), llvm::TargetMachine::getTargetIRAnalysis() const::{lambda(llvm::Function const&)#1}>::_M_invoke(std::_Any_data const&, llvm::Function const&) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
     n1: 131584 0x754A4F9: llvm::TargetTransformInfoWrapperPass::getTTI(llvm::Function const&) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
      n1: 131584 0x5E690A6: (anonymous namespace)::PreISelIntrinsicLowering::expandMemIntrinsicUses(llvm::Function&) const (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
       n1: 131584 0x5E6C9DC: (anonymous namespace)::PreISelIntrinsicLowering::lowerIntrinsics(llvm::Module&) const [clone .constprop.0] (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
        n1: 131584 0x5E6CDA9: (anonymous namespace)::PreISelIntrinsicLoweringLegacyPass::runOnModule(llvm::Module&) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
         n1: 131584 0x7811088: llvm::legacy::PassManagerImpl::run(llvm::Module&) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
          n1: 131584 0x5144B05: llvm::orc::SimpleCompiler::operator()(llvm::Module&) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
           n1: 131584 0x517AD3C: llvm::orc::IRCompileLayer::emit(std::unique_ptr<llvm::orc::MaterializationResponsibility, std::default_delete<llvm::orc::MaterializationResponsibility> >, llvm::orc::ThreadSafeModule) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
            n1: 131584 0x51B0088: llvm::orc::IRTransformLayer::emit(std::unique_ptr<llvm::orc::MaterializationResponsibility, std::default_delete<llvm::orc::MaterializationResponsibility> >, llvm::orc::ThreadSafeModule) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
             n1: 131584 0x51B0088: llvm::orc::IRTransformLayer::emit(std::unique_ptr<llvm::orc::MaterializationResponsibility, std::default_delete<llvm::orc::MaterializationResponsibility> >, llvm::orc::ThreadSafeModule) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
              n1: 131584 0x517FFB5: llvm::orc::BasicIRLayerMaterializationUnit::materialize(std::unique_ptr<llvm::orc::MaterializationResponsibility, std::default_delete<llvm::orc::MaterializationResponsibility> >) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
               n1: 131584 0x516363F: llvm::orc::MaterializationTask::run() (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
                n1: 131584 0x514864C: llvm::orc::ExecutionSession::dispatchTask(std::unique_ptr<llvm::orc::Task, std::default_delete<llvm::orc::Task> >) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
                 n1: 131584 0x516399B: llvm::orc::ExecutionSession::dispatchOutstandingMUs() (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
                  n1: 131584 0x5164EA4: llvm::orc::ExecutionSession::OL_completeLookup(std::unique_ptr<llvm::orc::InProgressLookupState, std::default_delete<llvm::orc::InProgressLookupState> >, std::shared_ptr<llvm::orc::AsynchronousSymbolQuery>, std::function<void (llvm::DenseMap<llvm::orc::JITDylib*, llvm::DenseSet<llvm::orc::SymbolStringPtr, llvm::DenseMapInfo<llvm::orc::SymbolStringPtr, void> >, llvm::DenseMapInfo<llvm::orc::JITDylib*, void>, llvm::detail::DenseMapPair<llvm::orc::JITDylib*, llvm::DenseSet<llvm::orc::SymbolStringPtr, llvm::DenseMapInfo<llvm::orc::SymbolStringPtr, void> > > > const&)>) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
                   n1: 131584 0x5165C73: llvm::orc::InProgressFullLookupState::complete(std::unique_ptr<llvm::orc::InProgressLookupState, std::default_delete<llvm::orc::InProgressLookupState> >) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
                    n1: 131584 0x515A75E: llvm::orc::ExecutionSession::OL_applyQueryPhase1(std::unique_ptr<llvm::orc::InProgressLookupState, std::default_delete<llvm::orc::InProgressLookupState> >, llvm::Error) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
                     n1: 131584 0x5166052: llvm::orc::ExecutionSession::lookup(llvm::orc::LookupKind, std::vector<std::pair<llvm::orc::JITDylib*, llvm::orc::JITDylibLookupFlags>, std::allocator<std::pair<llvm::orc::JITDylib*, llvm::orc::JITDylibLookupFlags> > > const&, llvm::orc::SymbolLookupSet, llvm::orc::SymbolState, llvm::unique_function<void (llvm::Expected<llvm::DenseMap<llvm::orc::SymbolStringPtr, llvm::orc::ExecutorSymbolDef, llvm::DenseMapInfo<llvm::orc::SymbolStringPtr, void>, llvm::detail::DenseMapPair<llvm::orc::SymbolStringPtr, llvm::orc::ExecutorSymbolDef> > >)>, std::function<void (llvm::DenseMap<llvm::orc::JITDylib*, llvm::DenseSet<llvm::orc::SymbolStringPtr, llvm::DenseMapInfo<llvm::orc::SymbolStringPtr, void> >, llvm::DenseMapInfo<llvm::orc::JITDylib*, void>, llvm::detail::DenseMapPair<llvm::orc::JITDylib*, llvm::DenseSet<llvm::orc::SymbolStringPtr, llvm::DenseMapInfo<llvm::orc::SymbolStringPtr, void> > > > const&)>) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
                      n1: 131584 0x5166327: llvm::orc::ExecutionSession::lookup(std::vector<std::pair<llvm::orc::JITDylib*, llvm::orc::JITDylibLookupFlags>, std::allocator<std::pair<llvm::orc::JITDylib*, llvm::orc::JITDylibLookupFlags> > > const&, llvm::orc::SymbolLookupSet, llvm::orc::LookupKind, llvm::orc::SymbolState, std::function<void (llvm::DenseMap<llvm::orc::JITDylib*, llvm::DenseSet<llvm::orc::SymbolStringPtr, llvm::DenseMapInfo<llvm::orc::SymbolStringPtr, void> >, llvm::DenseMapInfo<llvm::orc::JITDylib*, void>, llvm::detail::DenseMapPair<llvm::orc::JITDylib*, llvm::DenseSet<llvm::orc::SymbolStringPtr, llvm::DenseMapInfo<llvm::orc::SymbolStringPtr, void> > > > const&)>) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
                       n1: 131584 0x516671D: llvm::orc::ExecutionSession::lookup(std::vector<std::pair<llvm::orc::JITDylib*, llvm::orc::JITDylibLookupFlags>, std::allocator<std::pair<llvm::orc::JITDylib*, llvm::orc::JITDylibLookupFlags> > > const&, llvm::orc::SymbolStringPtr, llvm::orc::SymbolState) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
                        n1: 131584 0x5189AB9: llvm::orc::LLJIT::lookupLinkerMangled(llvm::orc::JITDylib&, llvm::orc::SymbolStringPtr) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
                         n1: 131584 0x4B9C824: mlir::ExecutionEngine::lookup(llvm::StringRef) const (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
                          n1: 131584 0x4B9CF78: mlir::ExecutionEngine::lookupPacked(llvm::StringRef) const (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
                           n1: 131584 0x4B6F974: compileAndExecute((anonymous namespace)::Options&, mlir::Operation*, llvm::StringRef, (anonymous namespace)::CompileAndExecuteConfig, void**, std::unique_ptr<llvm::TargetMachine, std::default_delete<llvm::TargetMachine> >) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
                            n1: 131584 0x4B6FF5C: compileAndExecuteVoidFunction((anonymous namespace)::Options&, mlir::Operation*, llvm::StringRef, (anonymous namespace)::CompileAndExecuteConfig, std::unique_ptr<llvm::TargetMachine, std::default_delete<llvm::TargetMachine> >) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
                             n1: 131584 0x4B715A2: mlir::JitRunnerMain(int, char**, mlir::DialectRegistry const&, mlir::JitRunnerConfig) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
                              n0: 131584 0x42B4749: main (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
 n2: 100008 0x455EC86: llvm::StringMapImpl::RehashTable(unsigned int) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
  n0: 50844 in 12 places, all below massif's threshold (1.00%)
  n1: 49164 0x452AB40: std::pair<llvm::StringMapIterator<llvm::cl::Option*>, bool> llvm::StringMap<llvm::cl::Option*, llvm::MallocAllocator>::try_emplace_with_hash<llvm::cl::Option*>(llvm::StringRef, unsigned int, llvm::cl::Option*&&) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
   n1: 49164 0x452C0B4: (anonymous namespace)::CommandLineParser::addOption(llvm::cl::Option*, llvm::cl::SubCommand*) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
    n2: 49164 0x452C945: llvm::cl::Option::addArgument() (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
     n1: 49164 0x432A78A: _GLOBAL__sub_I_InlineAdvisor.cpp (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
      n1: 49164 0x9CC8EBA: call_init (libc-start.c:145)
       n1: 49164 0x9CC8EBA: __libc_start_main@@GLIBC_2.34 (libc-start.c:379)
        n0: 49164 0x436AA04: (below main) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
     n0: 0 in 7 places, all below massif's threshold (1.00%)
 n1: 73728 0x459473A: RegisterHandlers() (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
  n1: 73728 0x45439EB: llvm::InitLLVM::InitLLVM(int&, char const**&, bool) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
   n0: 73728 0x42B465D: main (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
 n1: 72704 0x9A14939: ??? (in /usr/lib/x86_64-linux-gnu/libstdc++.so.6.0.30)
  n1: 72704 0x90E547D: call_init.part.0 (dl-init.c:70)
   n1: 72704 0x90E5567: call_init (dl-init.c:33)
    n1: 72704 0x90E5567: _dl_init (dl-init.c:117)
     n1: 72704 0x90FF2C9: ??? (in /usr/lib/x86_64-linux-gnu/ld-linux-x86-64.so.2)
      n1: 72704 0x6: ???
       n1: 72704 0x1FFEFFFDA6: ???
        n1: 72704 0x1FFEFFFDC7: ???
         n1: 72704 0x1FFEFFFDCB: ???
          n1: 72704 0x1FFEFFFDCE: ???
           n1: 72704 0x1FFEFFFDD3: ???
            n1: 72704 0x1FFEFFFDEC: ???
             n0: 72704 0x1FFEFFFE26: ???
 n1: 56160 0x9CE4791: __new_exitfn (cxa_atexit.c:114)
  n1: 56160 0x9CE490A: __internal_atexit (cxa_atexit.c:44)
   n1: 56160 0x9CE490A: __cxa_atexit (cxa_atexit.c:70)
    n0: 56160 in 40 places, all below massif's threshold (1.00%)
#-----------
snapshot=3
#-----------
time=89516978
mem_heap_B=3657104
mem_heap_extra_B=143456
mem_stacks_B=0
heap_tree=empty
#-----------
snapshot=4
#-----------
time=*********
mem_heap_B=4018504
mem_heap_extra_B=148160
mem_stacks_B=0
heap_tree=empty
#-----------
snapshot=5
#-----------
time=*********
mem_heap_B=4395126
mem_heap_extra_B=148778
mem_stacks_B=0
heap_tree=empty
#-----------
snapshot=6
#-----------
time=*********
mem_heap_B=5137856
mem_heap_extra_B=163912
mem_stacks_B=0
heap_tree=empty
#-----------
snapshot=7
#-----------
time=*********
mem_heap_B=4718368
mem_heap_extra_B=152144
mem_stacks_B=0
heap_tree=empty
#-----------
snapshot=8
#-----------
time=*********
mem_heap_B=4916276
mem_heap_extra_B=150340
mem_stacks_B=0
heap_tree=empty
#-----------
snapshot=9
#-----------
time=286215317
mem_heap_B=5186612
mem_heap_extra_B=150340
mem_stacks_B=0
heap_tree=empty
#-----------
snapshot=10
#-----------
time=340361937
mem_heap_B=5727284
mem_heap_extra_B=150340
mem_stacks_B=0
heap_tree=detailed
n13: 5727284 (heap allocation functions) malloc/new/new[], --alloc-fns, etc.
 n7: 1476445 0x4550A43: llvm::allocate_buffer(unsigned long, unsigned long) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
  n0: 608909 in 263 places, all below massif's threshold (1.00%)
  n1: 376832 0x4377AC6: llvm::BumpPtrAllocatorImpl<llvm::MallocAllocator, 4096ul, 4096ul, 128ul>::AllocateSlow(unsigned long, unsigned long, llvm::Align) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
   n0: 376832 in 63 places, all below massif's threshold (1.00%)
  n1: 132096 0x4B69874: mlir::StorageUniquer::getParametricStorageTypeImpl(mlir::TypeID, unsigned int, llvm::function_ref<bool (mlir::StorageUniquer::BaseStorage const*)>, llvm::function_ref<mlir::StorageUniquer::BaseStorage* (mlir::StorageUniquer::StorageAllocator&)>) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
   n0: 132096 in 25 places, all below massif's threshold (1.00%)
  n2: 110155 0x43EBEEF: llvm::ConstantDataSequential::getImpl(llvm::StringRef, llvm::Type*) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
   n1: 109695 0x43EE6B2: llvm::ConstantArray::getImpl(llvm::ArrayType*, llvm::ArrayRef<llvm::Constant*>) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
    n1: 109695 0x43F4B35: llvm::ConstantArray::get(llvm::ArrayType*, llvm::ArrayRef<llvm::Constant*>) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
     n2: 109695 0x4B42004: mlir::LLVM::detail::getLLVMConstant(llvm::Type*, mlir::Attribute, mlir::Location, mlir::LLVM::ModuleTranslation const&) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
      n2: 93294 0x4B40BA9: mlir::LLVM::detail::getLLVMConstant(llvm::Type*, mlir::Attribute, mlir::Location, mlir::LLVM::ModuleTranslation const&) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
       n1: 76851 0x4B483D2: mlir::LLVM::ModuleTranslation::convertGlobals() (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
        n1: 76851 0x4B55FCF: mlir::translateModuleToLLVMIR(mlir::Operation*, llvm::LLVMContext&, llvm::StringRef, bool) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
         n1: 76851 0x436B90C: convertMLIRModule(mlir::Operation*, llvm::LLVMContext&) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
          n1: 76851 0x436AAF8: std::unique_ptr<llvm::Module, std::default_delete<llvm::Module> > llvm::function_ref<std::unique_ptr<llvm::Module, std::default_delete<llvm::Module> > (mlir::Operation*, llvm::LLVMContext&)>::callback_fn<std::unique_ptr<llvm::Module, std::default_delete<llvm::Module> > (mlir::Operation*, llvm::LLVMContext&)>(long, mlir::Operation*, llvm::LLVMContext&) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
           n1: 76851 0x4B9EB60: mlir::ExecutionEngine::create(mlir::Operation*, mlir::ExecutionEngineOptions const&, std::unique_ptr<llvm::TargetMachine, std::default_delete<llvm::TargetMachine> >) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
            n1: 76851 0x4B6F8B5: compileAndExecute((anonymous namespace)::Options&, mlir::Operation*, llvm::StringRef, (anonymous namespace)::CompileAndExecuteConfig, void**, std::unique_ptr<llvm::TargetMachine, std::default_delete<llvm::TargetMachine> >) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
             n1: 76851 0x4B6FF5C: compileAndExecuteVoidFunction((anonymous namespace)::Options&, mlir::Operation*, llvm::StringRef, (anonymous namespace)::CompileAndExecuteConfig, std::unique_ptr<llvm::TargetMachine, std::default_delete<llvm::TargetMachine> >) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
              n1: 76851 0x4B715A2: mlir::JitRunnerMain(int, char**, mlir::DialectRegistry const&, mlir::JitRunnerConfig) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
               n0: 76851 0x42B4749: main (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
       n0: 16443 in 1 place, below massif's threshold (1.00%)
      n0: 16401 in 1 place, below massif's threshold (1.00%)
   n0: 460 in 2 places, all below massif's threshold (1.00%)
  n1: 107295 0x5177EAC: llvm::orc::getELFObjectFileSymbolInfo(llvm::orc::ExecutionSession&, llvm::object::ELFObjectFileBase const&) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
   n1: 107295 0x5179875: llvm::orc::getObjectFileInterface(llvm::orc::ExecutionSession&, llvm::MemoryBufferRef) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
    n1: 107295 0x5180A71: llvm::orc::ObjectLayer::add(llvm::orc::JITDylib&, std::unique_ptr<llvm::MemoryBuffer, std::default_delete<llvm::MemoryBuffer> >) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
     n1: 107295 0x4B9D938: mlir::ExecutionEngine::create(mlir::Operation*, mlir::ExecutionEngineOptions const&, std::unique_ptr<llvm::TargetMachine, std::default_delete<llvm::TargetMachine> >)::{lambda(llvm::orc::ExecutionSession&, llvm::Triple const&)#2}::operator()(llvm::orc::ExecutionSession&, llvm::Triple const&) const [clone .constprop.0] (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
      n1: 107295 0x4B9E087: std::_Function_handler<llvm::Expected<std::unique_ptr<llvm::orc::ObjectLayer, std::default_delete<llvm::orc::ObjectLayer> > > (llvm::orc::ExecutionSession&, llvm::Triple const&), mlir::ExecutionEngine::create(mlir::Operation*, mlir::ExecutionEngineOptions const&, std::unique_ptr<llvm::TargetMachine, std::default_delete<llvm::TargetMachine> >)::{lambda(llvm::orc::ExecutionSession&, llvm::Triple const&)#2}>::_M_invoke(std::_Any_data const&, llvm::orc::ExecutionSession&, llvm::Triple const&) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
       n1: 107295 0x5187AB0: llvm::orc::LLJIT::createObjectLinkingLayer(llvm::orc::LLJITBuilderState&, llvm::orc::ExecutionSession&) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
        n1: 107295 0x518D2D2: llvm::orc::LLJIT::LLJIT(llvm::orc::LLJITBuilderState&, llvm::Error&) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
         n1: 107295 0x4B9FB34: mlir::ExecutionEngine::create(mlir::Operation*, mlir::ExecutionEngineOptions const&, std::unique_ptr<llvm::TargetMachine, std::default_delete<llvm::TargetMachine> >) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
          n1: 107295 0x4B6F8B5: compileAndExecute((anonymous namespace)::Options&, mlir::Operation*, llvm::StringRef, (anonymous namespace)::CompileAndExecuteConfig, void**, std::unique_ptr<llvm::TargetMachine, std::default_delete<llvm::TargetMachine> >) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
           n1: 107295 0x4B6FF5C: compileAndExecuteVoidFunction((anonymous namespace)::Options&, mlir::Operation*, llvm::StringRef, (anonymous namespace)::CompileAndExecuteConfig, std::unique_ptr<llvm::TargetMachine, std::default_delete<llvm::TargetMachine> >) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
            n1: 107295 0x4B715A2: mlir::JitRunnerMain(int, char**, mlir::DialectRegistry const&, mlir::JitRunnerConfig) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
             n0: 107295 0x42B4749: main (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
  n1: 70656 0x4B68B07: mlir::StorageUniquer::getParametricStorageTypeImpl(mlir::TypeID, unsigned int, llvm::function_ref<bool (mlir::StorageUniquer::BaseStorage const*)>, llvm::function_ref<mlir::StorageUniquer::BaseStorage* (mlir::StorageUniquer::StorageAllocator&)>) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
   n0: 70656 in 22 places, all below massif's threshold (1.00%)
  n2: 70502 0x452AAFF: std::pair<llvm::StringMapIterator<llvm::cl::Option*>, bool> llvm::StringMap<llvm::cl::Option*, llvm::MallocAllocator>::try_emplace_with_hash<llvm::cl::Option*>(llvm::StringRef, unsigned int, llvm::cl::Option*&&) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
   n2: 70430 0x452C0B4: (anonymous namespace)::CommandLineParser::addOption(llvm::cl::Option*, llvm::cl::SubCommand*) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
    n1: 70222 0x452C945: llvm::cl::Option::addArgument() (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
     n0: 70222 in 1695 places, all below massif's threshold (1.00%)
    n0: 208 in 2 places, all below massif's threshold (1.00%)
   n0: 72 in 1 place, below massif's threshold (1.00%)
 n2: 1084894 0x45543E6: llvm::SmallVectorBase<unsigned long>::grow_pod(void*, unsigned long, unsigned long) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
  n1: 1081474 0x7700FA3: llvm::MCObjectStreamer::emitBytes(llvm::StringRef) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
   n2: 1081474 0x770E4E4: llvm::MCStreamer::emitIntValue(unsigned long, unsigned int) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
    n1: 1081343 0x4BA8889: emitGlobalConstantFP(llvm::APFloat, llvm::Type*, llvm::AsmPrinter&) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
     n1: 1081343 0x4BBE25B: emitGlobalConstantImpl(llvm::DataLayout const&, llvm::Constant const*, llvm::AsmPrinter&, llvm::Constant const*, unsigned long, llvm::DenseMap<unsigned long, llvm::SmallVector<llvm::GlobalAlias const*, 1u>, llvm::DenseMapInfo<unsigned long, void>, llvm::detail::DenseMapPair<unsigned long, llvm::SmallVector<llvm::GlobalAlias const*, 1u> > >*) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
      n1: 1081343 0x4BBEBD5: emitGlobalConstantImpl(llvm::DataLayout const&, llvm::Constant const*, llvm::AsmPrinter&, llvm::Constant const*, unsigned long, llvm::DenseMap<unsigned long, llvm::SmallVector<llvm::GlobalAlias const*, 1u>, llvm::DenseMapInfo<unsigned long, void>, llvm::detail::DenseMapPair<unsigned long, llvm::SmallVector<llvm::GlobalAlias const*, 1u> > >*) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
       n1: 1081343 0x4BBFD1B: llvm::AsmPrinter::emitGlobalVariable(llvm::GlobalVariable const*) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
        n1: 1081343 0x4BBBAC0: llvm::AsmPrinter::doFinalization(llvm::Module&) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
         n1: 1081343 0x7806D84: llvm::FPPassManager::doFinalization(llvm::Module&) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
          n1: 1081343 0x7811305: llvm::legacy::PassManagerImpl::run(llvm::Module&) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
           n1: 1081343 0x5144B05: llvm::orc::SimpleCompiler::operator()(llvm::Module&) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
            n1: 1081343 0x517AD3C: llvm::orc::IRCompileLayer::emit(std::unique_ptr<llvm::orc::MaterializationResponsibility, std::default_delete<llvm::orc::MaterializationResponsibility> >, llvm::orc::ThreadSafeModule) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
             n1: 1081343 0x51B0088: llvm::orc::IRTransformLayer::emit(std::unique_ptr<llvm::orc::MaterializationResponsibility, std::default_delete<llvm::orc::MaterializationResponsibility> >, llvm::orc::ThreadSafeModule) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
              n1: 1081343 0x51B0088: llvm::orc::IRTransformLayer::emit(std::unique_ptr<llvm::orc::MaterializationResponsibility, std::default_delete<llvm::orc::MaterializationResponsibility> >, llvm::orc::ThreadSafeModule) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
               n1: 1081343 0x517FFB5: llvm::orc::BasicIRLayerMaterializationUnit::materialize(std::unique_ptr<llvm::orc::MaterializationResponsibility, std::default_delete<llvm::orc::MaterializationResponsibility> >) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
                n1: 1081343 0x516363F: llvm::orc::MaterializationTask::run() (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
                 n1: 1081343 0x514864C: llvm::orc::ExecutionSession::dispatchTask(std::unique_ptr<llvm::orc::Task, std::default_delete<llvm::orc::Task> >) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
                  n1: 1081343 0x516399B: llvm::orc::ExecutionSession::dispatchOutstandingMUs() (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
                   n1: 1081343 0x5164EA4: llvm::orc::ExecutionSession::OL_completeLookup(std::unique_ptr<llvm::orc::InProgressLookupState, std::default_delete<llvm::orc::InProgressLookupState> >, std::shared_ptr<llvm::orc::AsynchronousSymbolQuery>, std::function<void (llvm::DenseMap<llvm::orc::JITDylib*, llvm::DenseSet<llvm::orc::SymbolStringPtr, llvm::DenseMapInfo<llvm::orc::SymbolStringPtr, void> >, llvm::DenseMapInfo<llvm::orc::JITDylib*, void>, llvm::detail::DenseMapPair<llvm::orc::JITDylib*, llvm::DenseSet<llvm::orc::SymbolStringPtr, llvm::DenseMapInfo<llvm::orc::SymbolStringPtr, void> > > > const&)>) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
                    n1: 1081343 0x5165C73: llvm::orc::InProgressFullLookupState::complete(std::unique_ptr<llvm::orc::InProgressLookupState, std::default_delete<llvm::orc::InProgressLookupState> >) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
                     n1: 1081343 0x515A75E: llvm::orc::ExecutionSession::OL_applyQueryPhase1(std::unique_ptr<llvm::orc::InProgressLookupState, std::default_delete<llvm::orc::InProgressLookupState> >, llvm::Error) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
                      n1: 1081343 0x5166052: llvm::orc::ExecutionSession::lookup(llvm::orc::LookupKind, std::vector<std::pair<llvm::orc::JITDylib*, llvm::orc::JITDylibLookupFlags>, std::allocator<std::pair<llvm::orc::JITDylib*, llvm::orc::JITDylibLookupFlags> > > const&, llvm::orc::SymbolLookupSet, llvm::orc::SymbolState, llvm::unique_function<void (llvm::Expected<llvm::DenseMap<llvm::orc::SymbolStringPtr, llvm::orc::ExecutorSymbolDef, llvm::DenseMapInfo<llvm::orc::SymbolStringPtr, void>, llvm::detail::DenseMapPair<llvm::orc::SymbolStringPtr, llvm::orc::ExecutorSymbolDef> > >)>, std::function<void (llvm::DenseMap<llvm::orc::JITDylib*, llvm::DenseSet<llvm::orc::SymbolStringPtr, llvm::DenseMapInfo<llvm::orc::SymbolStringPtr, void> >, llvm::DenseMapInfo<llvm::orc::JITDylib*, void>, llvm::detail::DenseMapPair<llvm::orc::JITDylib*, llvm::DenseSet<llvm::orc::SymbolStringPtr, llvm::DenseMapInfo<llvm::orc::SymbolStringPtr, void> > > > const&)>) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
                       n1: 1081343 0x5166327: llvm::orc::ExecutionSession::lookup(std::vector<std::pair<llvm::orc::JITDylib*, llvm::orc::JITDylibLookupFlags>, std::allocator<std::pair<llvm::orc::JITDylib*, llvm::orc::JITDylibLookupFlags> > > const&, llvm::orc::SymbolLookupSet, llvm::orc::LookupKind, llvm::orc::SymbolState, std::function<void (llvm::DenseMap<llvm::orc::JITDylib*, llvm::DenseSet<llvm::orc::SymbolStringPtr, llvm::DenseMapInfo<llvm::orc::SymbolStringPtr, void> >, llvm::DenseMapInfo<llvm::orc::JITDylib*, void>, llvm::detail::DenseMapPair<llvm::orc::JITDylib*, llvm::DenseSet<llvm::orc::SymbolStringPtr, llvm::DenseMapInfo<llvm::orc::SymbolStringPtr, void> > > > const&)>) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
                        n1: 1081343 0x516671D: llvm::orc::ExecutionSession::lookup(std::vector<std::pair<llvm::orc::JITDylib*, llvm::orc::JITDylibLookupFlags>, std::allocator<std::pair<llvm::orc::JITDylib*, llvm::orc::JITDylibLookupFlags> > > const&, llvm::orc::SymbolStringPtr, llvm::orc::SymbolState) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
                         n1: 1081343 0x5189AB9: llvm::orc::LLJIT::lookupLinkerMangled(llvm::orc::JITDylib&, llvm::orc::SymbolStringPtr) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
                          n1: 1081343 0x4B9C824: mlir::ExecutionEngine::lookup(llvm::StringRef) const (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
                           n1: 1081343 0x4B9CF78: mlir::ExecutionEngine::lookupPacked(llvm::StringRef) const (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
                            n1: 1081343 0x4B6F974: compileAndExecute((anonymous namespace)::Options&, mlir::Operation*, llvm::StringRef, (anonymous namespace)::CompileAndExecuteConfig, void**, std::unique_ptr<llvm::TargetMachine, std::default_delete<llvm::TargetMachine> >) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
                             n1: 1081343 0x4B6FF5C: compileAndExecuteVoidFunction((anonymous namespace)::Options&, mlir::Operation*, llvm::StringRef, (anonymous namespace)::CompileAndExecuteConfig, std::unique_ptr<llvm::TargetMachine, std::default_delete<llvm::TargetMachine> >) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
                              n0: 1081343 0x4B715A2: mlir::JitRunnerMain(int, char**, mlir::DialectRegistry const&, mlir::JitRunnerConfig) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
    n0: 131 in 1 place, below massif's threshold (1.00%)
  n0: 3420 in 9 places, all below massif's threshold (1.00%)
 n2: 957416 0x4383EBE: llvm::User::operator new(unsigned long, llvm::User::IntrusiveOperandsAllocMarker) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
  n2: 839144 0x43F4CC2: llvm::ConstantArray::get(llvm::ArrayType*, llvm::ArrayRef<llvm::Constant*>) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
   n2: 838488 0x4B42004: mlir::LLVM::detail::getLLVMConstant(llvm::Type*, mlir::Attribute, mlir::Location, mlir::LLVM::ModuleTranslation const&) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
    n1: 835824 0x4B483D2: mlir::LLVM::ModuleTranslation::convertGlobals() (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
     n1: 835824 0x4B55FCF: mlir::translateModuleToLLVMIR(mlir::Operation*, llvm::LLVMContext&, llvm::StringRef, bool) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
      n1: 835824 0x436B90C: convertMLIRModule(mlir::Operation*, llvm::LLVMContext&) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
       n1: 835824 0x436AAF8: std::unique_ptr<llvm::Module, std::default_delete<llvm::Module> > llvm::function_ref<std::unique_ptr<llvm::Module, std::default_delete<llvm::Module> > (mlir::Operation*, llvm::LLVMContext&)>::callback_fn<std::unique_ptr<llvm::Module, std::default_delete<llvm::Module> > (mlir::Operation*, llvm::LLVMContext&)>(long, mlir::Operation*, llvm::LLVMContext&) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
        n1: 835824 0x4B9EB60: mlir::ExecutionEngine::create(mlir::Operation*, mlir::ExecutionEngineOptions const&, std::unique_ptr<llvm::TargetMachine, std::default_delete<llvm::TargetMachine> >) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
         n1: 835824 0x4B6F8B5: compileAndExecute((anonymous namespace)::Options&, mlir::Operation*, llvm::StringRef, (anonymous namespace)::CompileAndExecuteConfig, void**, std::unique_ptr<llvm::TargetMachine, std::default_delete<llvm::TargetMachine> >) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
          n1: 835824 0x4B6FF5C: compileAndExecuteVoidFunction((anonymous namespace)::Options&, mlir::Operation*, llvm::StringRef, (anonymous namespace)::CompileAndExecuteConfig, std::unique_ptr<llvm::TargetMachine, std::default_delete<llvm::TargetMachine> >) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
           n1: 835824 0x4B715A2: mlir::JitRunnerMain(int, char**, mlir::DialectRegistry const&, mlir::JitRunnerConfig) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
            n0: 835824 0x42B4749: main (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
    n0: 2664 in 1 place, below massif's threshold (1.00%)
   n0: 656 in 1 place, below massif's threshold (1.00%)
  n0: 118272 in 42 places, all below massif's threshold (1.00%)
 n0: 752393 in 2199 places, all below massif's threshold (1.00%)
 n1: 413592 0x45C74E1: llvm::X86TargetMachine::getSubtargetImpl(llvm::Function const&) const (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
  n1: 413592 0x45C7822: llvm::X86TargetMachine::getTargetTransformInfo(llvm::Function const&) const (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
   n1: 413592 0x620EE97: std::_Function_handler<llvm::TargetTransformInfo (llvm::Function const&), llvm::TargetMachine::getTargetIRAnalysis() const::{lambda(llvm::Function const&)#1}>::_M_invoke(std::_Any_data const&, llvm::Function const&) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
    n1: 413592 0x754A4F9: llvm::TargetTransformInfoWrapperPass::getTTI(llvm::Function const&) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
     n1: 413592 0x5E690A6: (anonymous namespace)::PreISelIntrinsicLowering::expandMemIntrinsicUses(llvm::Function&) const (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
      n1: 413592 0x5E6C9DC: (anonymous namespace)::PreISelIntrinsicLowering::lowerIntrinsics(llvm::Module&) const [clone .constprop.0] (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
       n1: 413592 0x5E6CDA9: (anonymous namespace)::PreISelIntrinsicLoweringLegacyPass::runOnModule(llvm::Module&) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
        n1: 413592 0x7811088: llvm::legacy::PassManagerImpl::run(llvm::Module&) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
         n1: 413592 0x5144B05: llvm::orc::SimpleCompiler::operator()(llvm::Module&) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
          n1: 413592 0x517AD3C: llvm::orc::IRCompileLayer::emit(std::unique_ptr<llvm::orc::MaterializationResponsibility, std::default_delete<llvm::orc::MaterializationResponsibility> >, llvm::orc::ThreadSafeModule) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
           n1: 413592 0x51B0088: llvm::orc::IRTransformLayer::emit(std::unique_ptr<llvm::orc::MaterializationResponsibility, std::default_delete<llvm::orc::MaterializationResponsibility> >, llvm::orc::ThreadSafeModule) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
            n1: 413592 0x51B0088: llvm::orc::IRTransformLayer::emit(std::unique_ptr<llvm::orc::MaterializationResponsibility, std::default_delete<llvm::orc::MaterializationResponsibility> >, llvm::orc::ThreadSafeModule) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
             n1: 413592 0x517FFB5: llvm::orc::BasicIRLayerMaterializationUnit::materialize(std::unique_ptr<llvm::orc::MaterializationResponsibility, std::default_delete<llvm::orc::MaterializationResponsibility> >) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
              n1: 413592 0x516363F: llvm::orc::MaterializationTask::run() (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
               n1: 413592 0x514864C: llvm::orc::ExecutionSession::dispatchTask(std::unique_ptr<llvm::orc::Task, std::default_delete<llvm::orc::Task> >) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
                n1: 413592 0x516399B: llvm::orc::ExecutionSession::dispatchOutstandingMUs() (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
                 n1: 413592 0x5164EA4: llvm::orc::ExecutionSession::OL_completeLookup(std::unique_ptr<llvm::orc::InProgressLookupState, std::default_delete<llvm::orc::InProgressLookupState> >, std::shared_ptr<llvm::orc::AsynchronousSymbolQuery>, std::function<void (llvm::DenseMap<llvm::orc::JITDylib*, llvm::DenseSet<llvm::orc::SymbolStringPtr, llvm::DenseMapInfo<llvm::orc::SymbolStringPtr, void> >, llvm::DenseMapInfo<llvm::orc::JITDylib*, void>, llvm::detail::DenseMapPair<llvm::orc::JITDylib*, llvm::DenseSet<llvm::orc::SymbolStringPtr, llvm::DenseMapInfo<llvm::orc::SymbolStringPtr, void> > > > const&)>) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
                  n1: 413592 0x5165C73: llvm::orc::InProgressFullLookupState::complete(std::unique_ptr<llvm::orc::InProgressLookupState, std::default_delete<llvm::orc::InProgressLookupState> >) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
                   n1: 413592 0x515A75E: llvm::orc::ExecutionSession::OL_applyQueryPhase1(std::unique_ptr<llvm::orc::InProgressLookupState, std::default_delete<llvm::orc::InProgressLookupState> >, llvm::Error) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
                    n1: 413592 0x5166052: llvm::orc::ExecutionSession::lookup(llvm::orc::LookupKind, std::vector<std::pair<llvm::orc::JITDylib*, llvm::orc::JITDylibLookupFlags>, std::allocator<std::pair<llvm::orc::JITDylib*, llvm::orc::JITDylibLookupFlags> > > const&, llvm::orc::SymbolLookupSet, llvm::orc::SymbolState, llvm::unique_function<void (llvm::Expected<llvm::DenseMap<llvm::orc::SymbolStringPtr, llvm::orc::ExecutorSymbolDef, llvm::DenseMapInfo<llvm::orc::SymbolStringPtr, void>, llvm::detail::DenseMapPair<llvm::orc::SymbolStringPtr, llvm::orc::ExecutorSymbolDef> > >)>, std::function<void (llvm::DenseMap<llvm::orc::JITDylib*, llvm::DenseSet<llvm::orc::SymbolStringPtr, llvm::DenseMapInfo<llvm::orc::SymbolStringPtr, void> >, llvm::DenseMapInfo<llvm::orc::JITDylib*, void>, llvm::detail::DenseMapPair<llvm::orc::JITDylib*, llvm::DenseSet<llvm::orc::SymbolStringPtr, llvm::DenseMapInfo<llvm::orc::SymbolStringPtr, void> > > > const&)>) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
                     n1: 413592 0x5166327: llvm::orc::ExecutionSession::lookup(std::vector<std::pair<llvm::orc::JITDylib*, llvm::orc::JITDylibLookupFlags>, std::allocator<std::pair<llvm::orc::JITDylib*, llvm::orc::JITDylibLookupFlags> > > const&, llvm::orc::SymbolLookupSet, llvm::orc::LookupKind, llvm::orc::SymbolState, std::function<void (llvm::DenseMap<llvm::orc::JITDylib*, llvm::DenseSet<llvm::orc::SymbolStringPtr, llvm::DenseMapInfo<llvm::orc::SymbolStringPtr, void> >, llvm::DenseMapInfo<llvm::orc::JITDylib*, void>, llvm::detail::DenseMapPair<llvm::orc::JITDylib*, llvm::DenseSet<llvm::orc::SymbolStringPtr, llvm::DenseMapInfo<llvm::orc::SymbolStringPtr, void> > > > const&)>) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
                      n1: 413592 0x516671D: llvm::orc::ExecutionSession::lookup(std::vector<std::pair<llvm::orc::JITDylib*, llvm::orc::JITDylibLookupFlags>, std::allocator<std::pair<llvm::orc::JITDylib*, llvm::orc::JITDylibLookupFlags> > > const&, llvm::orc::SymbolStringPtr, llvm::orc::SymbolState) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
                       n1: 413592 0x5189AB9: llvm::orc::LLJIT::lookupLinkerMangled(llvm::orc::JITDylib&, llvm::orc::SymbolStringPtr) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
                        n1: 413592 0x4B9C824: mlir::ExecutionEngine::lookup(llvm::StringRef) const (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
                         n1: 413592 0x4B9CF78: mlir::ExecutionEngine::lookupPacked(llvm::StringRef) const (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
                          n1: 413592 0x4B6F974: compileAndExecute((anonymous namespace)::Options&, mlir::Operation*, llvm::StringRef, (anonymous namespace)::CompileAndExecuteConfig, void**, std::unique_ptr<llvm::TargetMachine, std::default_delete<llvm::TargetMachine> >) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
                           n1: 413592 0x4B6FF5C: compileAndExecuteVoidFunction((anonymous namespace)::Options&, mlir::Operation*, llvm::StringRef, (anonymous namespace)::CompileAndExecuteConfig, std::unique_ptr<llvm::TargetMachine, std::default_delete<llvm::TargetMachine> >) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
                            n1: 413592 0x4B715A2: mlir::JitRunnerMain(int, char**, mlir::DialectRegistry const&, mlir::JitRunnerConfig) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
                             n0: 413592 0x42B4749: main (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
 n2: 196608 0x48D3715: void std::vector<llvm::X86FoldTableEntry, std::allocator<llvm::X86FoldTableEntry> >::_M_realloc_insert<llvm::X86FoldTableEntry>(__gnu_cxx::__normal_iterator<llvm::X86FoldTableEntry*, std::vector<llvm::X86FoldTableEntry, std::allocator<llvm::X86FoldTableEntry> > >, llvm::X86FoldTableEntry&&) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
  n1: 196608 0x48D4B1E: llvm::lookupUnfoldTable(unsigned int) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
   n1: 196608 0x48D71C6: llvm::X86InstrInfo::getOpcodeAfterMemoryUnfold(unsigned int, bool, bool, unsigned int*) const (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
    n1: 196608 0x5DA6C06: (anonymous namespace)::MachineLICMImpl::Hoist(llvm::MachineInstr*, llvm::MachineBasicBlock*, llvm::MachineLoop*) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
     n1: 196608 0x5DA8AEF: (anonymous namespace)::MachineLICMImpl::run(llvm::MachineFunction&) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
      n1: 196608 0x5DA9F01: (anonymous namespace)::MachineLICMBase::runOnMachineFunction(llvm::MachineFunction&) [clone .part.0] (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
       n1: 196608 0x5D81566: llvm::MachineFunctionPass::runOnFunction(llvm::Function&) [clone .part.0] (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
        n1: 196608 0x7810644: llvm::FPPassManager::runOnFunction(llvm::Function&) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
         n1: 196608 0x7810AB8: llvm::FPPassManager::runOnModule(llvm::Module&) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
          n1: 196608 0x7811088: llvm::legacy::PassManagerImpl::run(llvm::Module&) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
           n1: 196608 0x5144B05: llvm::orc::SimpleCompiler::operator()(llvm::Module&) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
            n1: 196608 0x517AD3C: llvm::orc::IRCompileLayer::emit(std::unique_ptr<llvm::orc::MaterializationResponsibility, std::default_delete<llvm::orc::MaterializationResponsibility> >, llvm::orc::ThreadSafeModule) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
             n1: 196608 0x51B0088: llvm::orc::IRTransformLayer::emit(std::unique_ptr<llvm::orc::MaterializationResponsibility, std::default_delete<llvm::orc::MaterializationResponsibility> >, llvm::orc::ThreadSafeModule) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
              n1: 196608 0x51B0088: llvm::orc::IRTransformLayer::emit(std::unique_ptr<llvm::orc::MaterializationResponsibility, std::default_delete<llvm::orc::MaterializationResponsibility> >, llvm::orc::ThreadSafeModule) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
               n1: 196608 0x517FFB5: llvm::orc::BasicIRLayerMaterializationUnit::materialize(std::unique_ptr<llvm::orc::MaterializationResponsibility, std::default_delete<llvm::orc::MaterializationResponsibility> >) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
                n1: 196608 0x516363F: llvm::orc::MaterializationTask::run() (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
                 n1: 196608 0x514864C: llvm::orc::ExecutionSession::dispatchTask(std::unique_ptr<llvm::orc::Task, std::default_delete<llvm::orc::Task> >) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
                  n1: 196608 0x516399B: llvm::orc::ExecutionSession::dispatchOutstandingMUs() (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
                   n1: 196608 0x5164EA4: llvm::orc::ExecutionSession::OL_completeLookup(std::unique_ptr<llvm::orc::InProgressLookupState, std::default_delete<llvm::orc::InProgressLookupState> >, std::shared_ptr<llvm::orc::AsynchronousSymbolQuery>, std::function<void (llvm::DenseMap<llvm::orc::JITDylib*, llvm::DenseSet<llvm::orc::SymbolStringPtr, llvm::DenseMapInfo<llvm::orc::SymbolStringPtr, void> >, llvm::DenseMapInfo<llvm::orc::JITDylib*, void>, llvm::detail::DenseMapPair<llvm::orc::JITDylib*, llvm::DenseSet<llvm::orc::SymbolStringPtr, llvm::DenseMapInfo<llvm::orc::SymbolStringPtr, void> > > > const&)>) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
                    n1: 196608 0x5165C73: llvm::orc::InProgressFullLookupState::complete(std::unique_ptr<llvm::orc::InProgressLookupState, std::default_delete<llvm::orc::InProgressLookupState> >) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
                     n1: 196608 0x515A75E: llvm::orc::ExecutionSession::OL_applyQueryPhase1(std::unique_ptr<llvm::orc::InProgressLookupState, std::default_delete<llvm::orc::InProgressLookupState> >, llvm::Error) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
                      n1: 196608 0x5166052: llvm::orc::ExecutionSession::lookup(llvm::orc::LookupKind, std::vector<std::pair<llvm::orc::JITDylib*, llvm::orc::JITDylibLookupFlags>, std::allocator<std::pair<llvm::orc::JITDylib*, llvm::orc::JITDylibLookupFlags> > > const&, llvm::orc::SymbolLookupSet, llvm::orc::SymbolState, llvm::unique_function<void (llvm::Expected<llvm::DenseMap<llvm::orc::SymbolStringPtr, llvm::orc::ExecutorSymbolDef, llvm::DenseMapInfo<llvm::orc::SymbolStringPtr, void>, llvm::detail::DenseMapPair<llvm::orc::SymbolStringPtr, llvm::orc::ExecutorSymbolDef> > >)>, std::function<void (llvm::DenseMap<llvm::orc::JITDylib*, llvm::DenseSet<llvm::orc::SymbolStringPtr, llvm::DenseMapInfo<llvm::orc::SymbolStringPtr, void> >, llvm::DenseMapInfo<llvm::orc::JITDylib*, void>, llvm::detail::DenseMapPair<llvm::orc::JITDylib*, llvm::DenseSet<llvm::orc::SymbolStringPtr, llvm::DenseMapInfo<llvm::orc::SymbolStringPtr, void> > > > const&)>) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
                       n1: 196608 0x5166327: llvm::orc::ExecutionSession::lookup(std::vector<std::pair<llvm::orc::JITDylib*, llvm::orc::JITDylibLookupFlags>, std::allocator<std::pair<llvm::orc::JITDylib*, llvm::orc::JITDylibLookupFlags> > > const&, llvm::orc::SymbolLookupSet, llvm::orc::LookupKind, llvm::orc::SymbolState, std::function<void (llvm::DenseMap<llvm::orc::JITDylib*, llvm::DenseSet<llvm::orc::SymbolStringPtr, llvm::DenseMapInfo<llvm::orc::SymbolStringPtr, void> >, llvm::DenseMapInfo<llvm::orc::JITDylib*, void>, llvm::detail::DenseMapPair<llvm::orc::JITDylib*, llvm::DenseSet<llvm::orc::SymbolStringPtr, llvm::DenseMapInfo<llvm::orc::SymbolStringPtr, void> > > > const&)>) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
                        n1: 196608 0x516671D: llvm::orc::ExecutionSession::lookup(std::vector<std::pair<llvm::orc::JITDylib*, llvm::orc::JITDylibLookupFlags>, std::allocator<std::pair<llvm::orc::JITDylib*, llvm::orc::JITDylibLookupFlags> > > const&, llvm::orc::SymbolStringPtr, llvm::orc::SymbolState) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
                         n1: 196608 0x5189AB9: llvm::orc::LLJIT::lookupLinkerMangled(llvm::orc::JITDylib&, llvm::orc::SymbolStringPtr) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
                          n1: 196608 0x4B9C824: mlir::ExecutionEngine::lookup(llvm::StringRef) const (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
                           n1: 196608 0x4B9CF78: mlir::ExecutionEngine::lookupPacked(llvm::StringRef) const (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
                            n1: 196608 0x4B6F974: compileAndExecute((anonymous namespace)::Options&, mlir::Operation*, llvm::StringRef, (anonymous namespace)::CompileAndExecuteConfig, void**, std::unique_ptr<llvm::TargetMachine, std::default_delete<llvm::TargetMachine> >) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
                             n1: 196608 0x4B6FF5C: compileAndExecuteVoidFunction((anonymous namespace)::Options&, mlir::Operation*, llvm::StringRef, (anonymous namespace)::CompileAndExecuteConfig, std::unique_ptr<llvm::TargetMachine, std::default_delete<llvm::TargetMachine> >) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
                              n0: 196608 0x4B715A2: mlir::JitRunnerMain(int, char**, mlir::DialectRegistry const&, mlir::JitRunnerConfig) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
  n0: 0 in 4 places, all below massif's threshold (1.00%)
 n2: 161908 0x455491F: llvm::SmallVectorBase<unsigned int>::grow_pod(void*, unsigned long, unsigned long) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
  n1: 102240 0x6094DD2: llvm::InterferenceCache::Entry::reset(llvm::MCRegister, llvm::LiveIntervalUnion*, llvm::TargetRegisterInfo const*, llvm::MachineFunction const*) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
   n1: 102240 0x6094FA2: llvm::InterferenceCache::get(llvm::MCRegister) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
    n2: 102240 0x5EB1968: llvm::RAGreedy::calculateRegionSplitCostAroundReg(unsigned short, llvm::AllocationOrder&, llvm::BlockFrequency&, unsigned int&, unsigned int&) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
     n1: 92016 0x5EB2C55: llvm::RAGreedy::tryRegionSplit(llvm::LiveInterval const&, llvm::AllocationOrder&, llvm::SmallVectorImpl<llvm::Register>&) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
      n1: 92016 0x5EB30CF: llvm::RAGreedy::trySplit(llvm::LiveInterval const&, llvm::AllocationOrder&, llvm::SmallVectorImpl<llvm::Register>&, llvm::SmallSet<llvm::Register, 16u, std::less<llvm::Register> > const&) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
       n1: 92016 0x5EB8338: llvm::RAGreedy::selectOrSplitImpl(llvm::LiveInterval const&, llvm::SmallVectorImpl<llvm::Register>&, llvm::SmallSet<llvm::Register, 16u, std::less<llvm::Register> >&, llvm::SmallVector<std::pair<llvm::LiveInterval const*, llvm::MCRegister>, 8u>&, unsigned int) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
        n1: 92016 0x5EBA367: llvm::RAGreedy::selectOrSplit(llvm::LiveInterval const&, llvm::SmallVectorImpl<llvm::Register>&) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
         n1: 92016 0x619A346: llvm::RegAllocBase::allocatePhysRegs() (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
          n1: 92016 0x5EB6531: llvm::RAGreedy::runOnMachineFunction(llvm::MachineFunction&) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
           n1: 92016 0x5D81566: llvm::MachineFunctionPass::runOnFunction(llvm::Function&) [clone .part.0] (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
            n1: 92016 0x7810644: llvm::FPPassManager::runOnFunction(llvm::Function&) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
             n1: 92016 0x7810AB8: llvm::FPPassManager::runOnModule(llvm::Module&) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
              n1: 92016 0x7811088: llvm::legacy::PassManagerImpl::run(llvm::Module&) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
               n1: 92016 0x5144B05: llvm::orc::SimpleCompiler::operator()(llvm::Module&) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
                n1: 92016 0x517AD3C: llvm::orc::IRCompileLayer::emit(std::unique_ptr<llvm::orc::MaterializationResponsibility, std::default_delete<llvm::orc::MaterializationResponsibility> >, llvm::orc::ThreadSafeModule) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
                 n1: 92016 0x51B0088: llvm::orc::IRTransformLayer::emit(std::unique_ptr<llvm::orc::MaterializationResponsibility, std::default_delete<llvm::orc::MaterializationResponsibility> >, llvm::orc::ThreadSafeModule) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
                  n1: 92016 0x51B0088: llvm::orc::IRTransformLayer::emit(std::unique_ptr<llvm::orc::MaterializationResponsibility, std::default_delete<llvm::orc::MaterializationResponsibility> >, llvm::orc::ThreadSafeModule) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
                   n1: 92016 0x517FFB5: llvm::orc::BasicIRLayerMaterializationUnit::materialize(std::unique_ptr<llvm::orc::MaterializationResponsibility, std::default_delete<llvm::orc::MaterializationResponsibility> >) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
                    n1: 92016 0x516363F: llvm::orc::MaterializationTask::run() (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
                     n1: 92016 0x514864C: llvm::orc::ExecutionSession::dispatchTask(std::unique_ptr<llvm::orc::Task, std::default_delete<llvm::orc::Task> >) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
                      n1: 92016 0x516399B: llvm::orc::ExecutionSession::dispatchOutstandingMUs() (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
                       n1: 92016 0x5164EA4: llvm::orc::ExecutionSession::OL_completeLookup(std::unique_ptr<llvm::orc::InProgressLookupState, std::default_delete<llvm::orc::InProgressLookupState> >, std::shared_ptr<llvm::orc::AsynchronousSymbolQuery>, std::function<void (llvm::DenseMap<llvm::orc::JITDylib*, llvm::DenseSet<llvm::orc::SymbolStringPtr, llvm::DenseMapInfo<llvm::orc::SymbolStringPtr, void> >, llvm::DenseMapInfo<llvm::orc::JITDylib*, void>, llvm::detail::DenseMapPair<llvm::orc::JITDylib*, llvm::DenseSet<llvm::orc::SymbolStringPtr, llvm::DenseMapInfo<llvm::orc::SymbolStringPtr, void> > > > const&)>) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
                        n1: 92016 0x5165C73: llvm::orc::InProgressFullLookupState::complete(std::unique_ptr<llvm::orc::InProgressLookupState, std::default_delete<llvm::orc::InProgressLookupState> >) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
                         n1: 92016 0x515A75E: llvm::orc::ExecutionSession::OL_applyQueryPhase1(std::unique_ptr<llvm::orc::InProgressLookupState, std::default_delete<llvm::orc::InProgressLookupState> >, llvm::Error) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
                          n1: 92016 0x5166052: llvm::orc::ExecutionSession::lookup(llvm::orc::LookupKind, std::vector<std::pair<llvm::orc::JITDylib*, llvm::orc::JITDylibLookupFlags>, std::allocator<std::pair<llvm::orc::JITDylib*, llvm::orc::JITDylibLookupFlags> > > const&, llvm::orc::SymbolLookupSet, llvm::orc::SymbolState, llvm::unique_function<void (llvm::Expected<llvm::DenseMap<llvm::orc::SymbolStringPtr, llvm::orc::ExecutorSymbolDef, llvm::DenseMapInfo<llvm::orc::SymbolStringPtr, void>, llvm::detail::DenseMapPair<llvm::orc::SymbolStringPtr, llvm::orc::ExecutorSymbolDef> > >)>, std::function<void (llvm::DenseMap<llvm::orc::JITDylib*, llvm::DenseSet<llvm::orc::SymbolStringPtr, llvm::DenseMapInfo<llvm::orc::SymbolStringPtr, void> >, llvm::DenseMapInfo<llvm::orc::JITDylib*, void>, llvm::detail::DenseMapPair<llvm::orc::JITDylib*, llvm::DenseSet<llvm::orc::SymbolStringPtr, llvm::DenseMapInfo<llvm::orc::SymbolStringPtr, void> > > > const&)>) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
                           n1: 92016 0x5166327: llvm::orc::ExecutionSession::lookup(std::vector<std::pair<llvm::orc::JITDylib*, llvm::orc::JITDylibLookupFlags>, std::allocator<std::pair<llvm::orc::JITDylib*, llvm::orc::JITDylibLookupFlags> > > const&, llvm::orc::SymbolLookupSet, llvm::orc::LookupKind, llvm::orc::SymbolState, std::function<void (llvm::DenseMap<llvm::orc::JITDylib*, llvm::DenseSet<llvm::orc::SymbolStringPtr, llvm::DenseMapInfo<llvm::orc::SymbolStringPtr, void> >, llvm::DenseMapInfo<llvm::orc::JITDylib*, void>, llvm::detail::DenseMapPair<llvm::orc::JITDylib*, llvm::DenseSet<llvm::orc::SymbolStringPtr, llvm::DenseMapInfo<llvm::orc::SymbolStringPtr, void> > > > const&)>) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
                            n1: 92016 0x516671D: llvm::orc::ExecutionSession::lookup(std::vector<std::pair<llvm::orc::JITDylib*, llvm::orc::JITDylibLookupFlags>, std::allocator<std::pair<llvm::orc::JITDylib*, llvm::orc::JITDylibLookupFlags> > > const&, llvm::orc::SymbolStringPtr, llvm::orc::SymbolState) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
                             n1: 92016 0x5189AB9: llvm::orc::LLJIT::lookupLinkerMangled(llvm::orc::JITDylib&, llvm::orc::SymbolStringPtr) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
                              n0: 92016 0x4B9C824: mlir::ExecutionEngine::lookup(llvm::StringRef) const (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
     n0: 10224 in 1 place, below massif's threshold (1.00%)
  n0: 59668 in 286 places, all below massif's threshold (1.00%)
 n2: 152832 0x4554A9A: llvm::SmallVectorBase<unsigned int>::mallocForGrow(void*, unsigned long, unsigned long, unsigned long&) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
  n0: 80984 in 54 places, all below massif's threshold (1.00%)
  n1: 71848 0x5D053CC: llvm::LiveVariables::getVarInfo(llvm::Register) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
   n1: 71848 0x5D07BE3: llvm::LiveVariables::runOnInstr(llvm::MachineInstr&, llvm::SmallVectorImpl<llvm::Register>&, unsigned int) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
    n1: 71848 0x5D081DB: llvm::LiveVariables::runOnBlock(llvm::MachineBasicBlock*, unsigned int) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
     n1: 71848 0x5D09228: llvm::LiveVariables::analyze(llvm::MachineFunction&) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
      n1: 71848 0x5D09910: llvm::LiveVariablesWrapperPass::runOnMachineFunction(llvm::MachineFunction&) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
       n1: 71848 0x5D81566: llvm::MachineFunctionPass::runOnFunction(llvm::Function&) [clone .part.0] (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
        n1: 71848 0x7810644: llvm::FPPassManager::runOnFunction(llvm::Function&) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
         n1: 71848 0x7810AB8: llvm::FPPassManager::runOnModule(llvm::Module&) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
          n1: 71848 0x7811088: llvm::legacy::PassManagerImpl::run(llvm::Module&) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
           n1: 71848 0x5144B05: llvm::orc::SimpleCompiler::operator()(llvm::Module&) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
            n1: 71848 0x517AD3C: llvm::orc::IRCompileLayer::emit(std::unique_ptr<llvm::orc::MaterializationResponsibility, std::default_delete<llvm::orc::MaterializationResponsibility> >, llvm::orc::ThreadSafeModule) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
             n1: 71848 0x51B0088: llvm::orc::IRTransformLayer::emit(std::unique_ptr<llvm::orc::MaterializationResponsibility, std::default_delete<llvm::orc::MaterializationResponsibility> >, llvm::orc::ThreadSafeModule) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
              n1: 71848 0x51B0088: llvm::orc::IRTransformLayer::emit(std::unique_ptr<llvm::orc::MaterializationResponsibility, std::default_delete<llvm::orc::MaterializationResponsibility> >, llvm::orc::ThreadSafeModule) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
               n1: 71848 0x517FFB5: llvm::orc::BasicIRLayerMaterializationUnit::materialize(std::unique_ptr<llvm::orc::MaterializationResponsibility, std::default_delete<llvm::orc::MaterializationResponsibility> >) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
                n1: 71848 0x516363F: llvm::orc::MaterializationTask::run() (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
                 n1: 71848 0x514864C: llvm::orc::ExecutionSession::dispatchTask(std::unique_ptr<llvm::orc::Task, std::default_delete<llvm::orc::Task> >) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
                  n1: 71848 0x516399B: llvm::orc::ExecutionSession::dispatchOutstandingMUs() (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
                   n1: 71848 0x5164EA4: llvm::orc::ExecutionSession::OL_completeLookup(std::unique_ptr<llvm::orc::InProgressLookupState, std::default_delete<llvm::orc::InProgressLookupState> >, std::shared_ptr<llvm::orc::AsynchronousSymbolQuery>, std::function<void (llvm::DenseMap<llvm::orc::JITDylib*, llvm::DenseSet<llvm::orc::SymbolStringPtr, llvm::DenseMapInfo<llvm::orc::SymbolStringPtr, void> >, llvm::DenseMapInfo<llvm::orc::JITDylib*, void>, llvm::detail::DenseMapPair<llvm::orc::JITDylib*, llvm::DenseSet<llvm::orc::SymbolStringPtr, llvm::DenseMapInfo<llvm::orc::SymbolStringPtr, void> > > > const&)>) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
                    n1: 71848 0x5165C73: llvm::orc::InProgressFullLookupState::complete(std::unique_ptr<llvm::orc::InProgressLookupState, std::default_delete<llvm::orc::InProgressLookupState> >) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
                     n1: 71848 0x515A75E: llvm::orc::ExecutionSession::OL_applyQueryPhase1(std::unique_ptr<llvm::orc::InProgressLookupState, std::default_delete<llvm::orc::InProgressLookupState> >, llvm::Error) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
                      n1: 71848 0x5166052: llvm::orc::ExecutionSession::lookup(llvm::orc::LookupKind, std::vector<std::pair<llvm::orc::JITDylib*, llvm::orc::JITDylibLookupFlags>, std::allocator<std::pair<llvm::orc::JITDylib*, llvm::orc::JITDylibLookupFlags> > > const&, llvm::orc::SymbolLookupSet, llvm::orc::SymbolState, llvm::unique_function<void (llvm::Expected<llvm::DenseMap<llvm::orc::SymbolStringPtr, llvm::orc::ExecutorSymbolDef, llvm::DenseMapInfo<llvm::orc::SymbolStringPtr, void>, llvm::detail::DenseMapPair<llvm::orc::SymbolStringPtr, llvm::orc::ExecutorSymbolDef> > >)>, std::function<void (llvm::DenseMap<llvm::orc::JITDylib*, llvm::DenseSet<llvm::orc::SymbolStringPtr, llvm::DenseMapInfo<llvm::orc::SymbolStringPtr, void> >, llvm::DenseMapInfo<llvm::orc::JITDylib*, void>, llvm::detail::DenseMapPair<llvm::orc::JITDylib*, llvm::DenseSet<llvm::orc::SymbolStringPtr, llvm::DenseMapInfo<llvm::orc::SymbolStringPtr, void> > > > const&)>) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
                       n1: 71848 0x5166327: llvm::orc::ExecutionSession::lookup(std::vector<std::pair<llvm::orc::JITDylib*, llvm::orc::JITDylibLookupFlags>, std::allocator<std::pair<llvm::orc::JITDylib*, llvm::orc::JITDylibLookupFlags> > > const&, llvm::orc::SymbolLookupSet, llvm::orc::LookupKind, llvm::orc::SymbolState, std::function<void (llvm::DenseMap<llvm::orc::JITDylib*, llvm::DenseSet<llvm::orc::SymbolStringPtr, llvm::DenseMapInfo<llvm::orc::SymbolStringPtr, void> >, llvm::DenseMapInfo<llvm::orc::JITDylib*, void>, llvm::detail::DenseMapPair<llvm::orc::JITDylib*, llvm::DenseSet<llvm::orc::SymbolStringPtr, llvm::DenseMapInfo<llvm::orc::SymbolStringPtr, void> > > > const&)>) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
                        n1: 71848 0x516671D: llvm::orc::ExecutionSession::lookup(std::vector<std::pair<llvm::orc::JITDylib*, llvm::orc::JITDylibLookupFlags>, std::allocator<std::pair<llvm::orc::JITDylib*, llvm::orc::JITDylibLookupFlags> > > const&, llvm::orc::SymbolStringPtr, llvm::orc::SymbolState) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
                         n1: 71848 0x5189AB9: llvm::orc::LLJIT::lookupLinkerMangled(llvm::orc::JITDylib&, llvm::orc::SymbolStringPtr) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
                          n1: 71848 0x4B9C824: mlir::ExecutionEngine::lookup(llvm::StringRef) const (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
                           n1: 71848 0x4B9CF78: mlir::ExecutionEngine::lookupPacked(llvm::StringRef) const (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
                            n1: 71848 0x4B6F974: compileAndExecute((anonymous namespace)::Options&, mlir::Operation*, llvm::StringRef, (anonymous namespace)::CompileAndExecuteConfig, void**, std::unique_ptr<llvm::TargetMachine, std::default_delete<llvm::TargetMachine> >) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
                             n1: 71848 0x4B6FF5C: compileAndExecuteVoidFunction((anonymous namespace)::Options&, mlir::Operation*, llvm::StringRef, (anonymous namespace)::CompileAndExecuteConfig, std::unique_ptr<llvm::TargetMachine, std::default_delete<llvm::TargetMachine> >) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
                              n0: 71848 0x4B715A2: mlir::JitRunnerMain(int, char**, mlir::DialectRegistry const&, mlir::JitRunnerConfig) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
 n1: 150088 0x4AB6764: mlir::Operation::create(mlir::Location, mlir::OperationName, mlir::TypeRange, mlir::ValueRange, mlir::DictionaryAttr, mlir::OpaqueProperties, mlir::BlockRange, unsigned int) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
  n1: 150088 0x4AB7B01: mlir::Operation::create(mlir::Location, mlir::OperationName, mlir::TypeRange, mlir::ValueRange, mlir::NamedAttrList&&, mlir::OpaqueProperties, mlir::BlockRange, mlir::RegionRange) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
   n1: 150088 0x4AB7EAD: mlir::Operation::create(mlir::OperationState const&) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
    n2: 150088 0x4B03AB2: mlir::OpBuilder::create(mlir::OperationState const&) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
     n2: 149032 0x5B1049F: (anonymous namespace)::OperationParser::parseOperation() (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
      n1: 123200 0x5B12DCC: (anonymous namespace)::OperationParser::parseBlock(mlir::Block*&) [clone .part.0] (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
       n1: 123200 0x5B13315: (anonymous namespace)::OperationParser::parseRegionBody(mlir::Region&, llvm::SMLoc, llvm::ArrayRef<mlir::OpAsmParser::Argument>, bool) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
        n1: 123200 0x5B139E1: (anonymous namespace)::OperationParser::parseRegion(mlir::Region&, llvm::ArrayRef<mlir::OpAsmParser::Argument>, bool) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
         n1: 123200 0x5B150B2: (anonymous namespace)::CustomOpAsmParser::parseOptionalRegion(mlir::Region&, llvm::ArrayRef<mlir::OpAsmParser::Argument>, bool) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
          n1: 123200 0x7170E1D: mlir::LLVM::LLVMFuncOp::parse(mlir::OpAsmParser&, mlir::OperationState&) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
           n1: 123200 0x5B0FDA9: (anonymous namespace)::OperationParser::parseOperation() (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
            n1: 123200 0x5B131FF: (anonymous namespace)::OperationParser::parseRegionBody(mlir::Region&, llvm::SMLoc, llvm::ArrayRef<mlir::OpAsmParser::Argument>, bool) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
             n1: 123200 0x5B152BA: (anonymous namespace)::CustomOpAsmParser::parseRegion(mlir::Region&, llvm::ArrayRef<mlir::OpAsmParser::Argument>, bool) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
              n1: 123200 0x4A5DF1C: mlir::ModuleOp::parse(mlir::OpAsmParser&, mlir::OperationState&) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
               n1: 123200 0x5B0FDA9: (anonymous namespace)::OperationParser::parseOperation() (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
                n1: 123200 0x5B11687: mlir::parseAsmSourceFile(llvm::SourceMgr const&, mlir::Block*, mlir::ParserConfig const&, mlir::AsmParserState*, mlir::AsmParserCodeCompleteContext*) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
                 n1: 123200 0x4B6E831: parseMLIRInput(llvm::StringRef, bool, mlir::MLIRContext*) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
                  n1: 123200 0x4B712A1: mlir::JitRunnerMain(int, char**, mlir::DialectRegistry const&, mlir::JitRunnerConfig) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
                   n0: 123200 0x42B4749: main (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
      n0: 25832 in 2 places, all below massif's threshold (1.00%)
     n0: 1056 in 2 places, all below massif's threshold (1.00%)
 n1: 131584 0x493B37D: llvm::X86Subtarget::X86Subtarget(llvm::Triple const&, llvm::StringRef, llvm::StringRef, llvm::StringRef, llvm::X86TargetMachine const&, llvm::MaybeAlign, unsigned int, unsigned int) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
  n1: 131584 0x45C7521: llvm::X86TargetMachine::getSubtargetImpl(llvm::Function const&) const (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
   n1: 131584 0x45C7822: llvm::X86TargetMachine::getTargetTransformInfo(llvm::Function const&) const (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
    n1: 131584 0x620EE97: std::_Function_handler<llvm::TargetTransformInfo (llvm::Function const&), llvm::TargetMachine::getTargetIRAnalysis() const::{lambda(llvm::Function const&)#1}>::_M_invoke(std::_Any_data const&, llvm::Function const&) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
     n1: 131584 0x754A4F9: llvm::TargetTransformInfoWrapperPass::getTTI(llvm::Function const&) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
      n1: 131584 0x5E690A6: (anonymous namespace)::PreISelIntrinsicLowering::expandMemIntrinsicUses(llvm::Function&) const (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
       n1: 131584 0x5E6C9DC: (anonymous namespace)::PreISelIntrinsicLowering::lowerIntrinsics(llvm::Module&) const [clone .constprop.0] (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
        n1: 131584 0x5E6CDA9: (anonymous namespace)::PreISelIntrinsicLoweringLegacyPass::runOnModule(llvm::Module&) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
         n1: 131584 0x7811088: llvm::legacy::PassManagerImpl::run(llvm::Module&) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
          n1: 131584 0x5144B05: llvm::orc::SimpleCompiler::operator()(llvm::Module&) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
           n1: 131584 0x517AD3C: llvm::orc::IRCompileLayer::emit(std::unique_ptr<llvm::orc::MaterializationResponsibility, std::default_delete<llvm::orc::MaterializationResponsibility> >, llvm::orc::ThreadSafeModule) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
            n1: 131584 0x51B0088: llvm::orc::IRTransformLayer::emit(std::unique_ptr<llvm::orc::MaterializationResponsibility, std::default_delete<llvm::orc::MaterializationResponsibility> >, llvm::orc::ThreadSafeModule) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
             n1: 131584 0x51B0088: llvm::orc::IRTransformLayer::emit(std::unique_ptr<llvm::orc::MaterializationResponsibility, std::default_delete<llvm::orc::MaterializationResponsibility> >, llvm::orc::ThreadSafeModule) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
              n1: 131584 0x517FFB5: llvm::orc::BasicIRLayerMaterializationUnit::materialize(std::unique_ptr<llvm::orc::MaterializationResponsibility, std::default_delete<llvm::orc::MaterializationResponsibility> >) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
               n1: 131584 0x516363F: llvm::orc::MaterializationTask::run() (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
                n1: 131584 0x514864C: llvm::orc::ExecutionSession::dispatchTask(std::unique_ptr<llvm::orc::Task, std::default_delete<llvm::orc::Task> >) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
                 n1: 131584 0x516399B: llvm::orc::ExecutionSession::dispatchOutstandingMUs() (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
                  n1: 131584 0x5164EA4: llvm::orc::ExecutionSession::OL_completeLookup(std::unique_ptr<llvm::orc::InProgressLookupState, std::default_delete<llvm::orc::InProgressLookupState> >, std::shared_ptr<llvm::orc::AsynchronousSymbolQuery>, std::function<void (llvm::DenseMap<llvm::orc::JITDylib*, llvm::DenseSet<llvm::orc::SymbolStringPtr, llvm::DenseMapInfo<llvm::orc::SymbolStringPtr, void> >, llvm::DenseMapInfo<llvm::orc::JITDylib*, void>, llvm::detail::DenseMapPair<llvm::orc::JITDylib*, llvm::DenseSet<llvm::orc::SymbolStringPtr, llvm::DenseMapInfo<llvm::orc::SymbolStringPtr, void> > > > const&)>) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
                   n1: 131584 0x5165C73: llvm::orc::InProgressFullLookupState::complete(std::unique_ptr<llvm::orc::InProgressLookupState, std::default_delete<llvm::orc::InProgressLookupState> >) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
                    n1: 131584 0x515A75E: llvm::orc::ExecutionSession::OL_applyQueryPhase1(std::unique_ptr<llvm::orc::InProgressLookupState, std::default_delete<llvm::orc::InProgressLookupState> >, llvm::Error) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
                     n1: 131584 0x5166052: llvm::orc::ExecutionSession::lookup(llvm::orc::LookupKind, std::vector<std::pair<llvm::orc::JITDylib*, llvm::orc::JITDylibLookupFlags>, std::allocator<std::pair<llvm::orc::JITDylib*, llvm::orc::JITDylibLookupFlags> > > const&, llvm::orc::SymbolLookupSet, llvm::orc::SymbolState, llvm::unique_function<void (llvm::Expected<llvm::DenseMap<llvm::orc::SymbolStringPtr, llvm::orc::ExecutorSymbolDef, llvm::DenseMapInfo<llvm::orc::SymbolStringPtr, void>, llvm::detail::DenseMapPair<llvm::orc::SymbolStringPtr, llvm::orc::ExecutorSymbolDef> > >)>, std::function<void (llvm::DenseMap<llvm::orc::JITDylib*, llvm::DenseSet<llvm::orc::SymbolStringPtr, llvm::DenseMapInfo<llvm::orc::SymbolStringPtr, void> >, llvm::DenseMapInfo<llvm::orc::JITDylib*, void>, llvm::detail::DenseMapPair<llvm::orc::JITDylib*, llvm::DenseSet<llvm::orc::SymbolStringPtr, llvm::DenseMapInfo<llvm::orc::SymbolStringPtr, void> > > > const&)>) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
                      n1: 131584 0x5166327: llvm::orc::ExecutionSession::lookup(std::vector<std::pair<llvm::orc::JITDylib*, llvm::orc::JITDylibLookupFlags>, std::allocator<std::pair<llvm::orc::JITDylib*, llvm::orc::JITDylibLookupFlags> > > const&, llvm::orc::SymbolLookupSet, llvm::orc::LookupKind, llvm::orc::SymbolState, std::function<void (llvm::DenseMap<llvm::orc::JITDylib*, llvm::DenseSet<llvm::orc::SymbolStringPtr, llvm::DenseMapInfo<llvm::orc::SymbolStringPtr, void> >, llvm::DenseMapInfo<llvm::orc::JITDylib*, void>, llvm::detail::DenseMapPair<llvm::orc::JITDylib*, llvm::DenseSet<llvm::orc::SymbolStringPtr, llvm::DenseMapInfo<llvm::orc::SymbolStringPtr, void> > > > const&)>) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
                       n1: 131584 0x516671D: llvm::orc::ExecutionSession::lookup(std::vector<std::pair<llvm::orc::JITDylib*, llvm::orc::JITDylibLookupFlags>, std::allocator<std::pair<llvm::orc::JITDylib*, llvm::orc::JITDylibLookupFlags> > > const&, llvm::orc::SymbolStringPtr, llvm::orc::SymbolState) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
                        n1: 131584 0x5189AB9: llvm::orc::LLJIT::lookupLinkerMangled(llvm::orc::JITDylib&, llvm::orc::SymbolStringPtr) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
                         n1: 131584 0x4B9C824: mlir::ExecutionEngine::lookup(llvm::StringRef) const (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
                          n1: 131584 0x4B9CF78: mlir::ExecutionEngine::lookupPacked(llvm::StringRef) const (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
                           n1: 131584 0x4B6F974: compileAndExecute((anonymous namespace)::Options&, mlir::Operation*, llvm::StringRef, (anonymous namespace)::CompileAndExecuteConfig, void**, std::unique_ptr<llvm::TargetMachine, std::default_delete<llvm::TargetMachine> >) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
                            n1: 131584 0x4B6FF5C: compileAndExecuteVoidFunction((anonymous namespace)::Options&, mlir::Operation*, llvm::StringRef, (anonymous namespace)::CompileAndExecuteConfig, std::unique_ptr<llvm::TargetMachine, std::default_delete<llvm::TargetMachine> >) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
                             n1: 131584 0x4B715A2: mlir::JitRunnerMain(int, char**, mlir::DialectRegistry const&, mlir::JitRunnerConfig) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
                              n0: 131584 0x42B4749: main (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
 n1: 103092 0x455EC86: llvm::StringMapImpl::RehashTable(unsigned int) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
  n0: 103092 in 14 places, all below massif's threshold (1.00%)
 n1: 73728 0x459473A: RegisterHandlers() (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
  n1: 73728 0x45439EB: llvm::InitLLVM::InitLLVM(int&, char const**&, bool) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
   n0: 73728 0x42B465D: main (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
 n1: 72704 0x9A14939: ??? (in /usr/lib/x86_64-linux-gnu/libstdc++.so.6.0.30)
  n1: 72704 0x90E547D: call_init.part.0 (dl-init.c:70)
   n1: 72704 0x90E5567: call_init (dl-init.c:33)
    n1: 72704 0x90E5567: _dl_init (dl-init.c:117)
     n1: 72704 0x90FF2C9: ??? (in /usr/lib/x86_64-linux-gnu/ld-linux-x86-64.so.2)
      n1: 72704 0x6: ???
       n1: 72704 0x1FFEFFFDA6: ???
        n1: 72704 0x1FFEFFFDC7: ???
         n1: 72704 0x1FFEFFFDCB: ???
          n1: 72704 0x1FFEFFFDCE: ???
           n1: 72704 0x1FFEFFFDD3: ???
            n1: 72704 0x1FFEFFFDEC: ???
             n0: 72704 0x1FFEFFFE26: ???
#-----------
snapshot=11
#-----------
time=448655089
mem_heap_B=6808628
mem_heap_extra_B=150340
mem_stacks_B=0
heap_tree=empty
#-----------
snapshot=12
#-----------
time=665243263
mem_heap_B=8971316
mem_heap_extra_B=154372
mem_stacks_B=0
heap_tree=empty
#-----------
snapshot=13
#-----------
time=1098417565
mem_heap_B=13296692
mem_heap_extra_B=154372
mem_stacks_B=0
heap_tree=empty
#-----------
snapshot=14
#-----------
time=1964766081
mem_heap_B=21947444
mem_heap_extra_B=154372
mem_stacks_B=0
heap_tree=empty
#-----------
snapshot=15
#-----------
time=3697463025
mem_heap_B=39248948
mem_heap_extra_B=154372
mem_stacks_B=0
heap_tree=empty
#-----------
snapshot=16
#-----------
time=7162858783
mem_heap_B=73851956
mem_heap_extra_B=154372
mem_stacks_B=0
heap_tree=empty
#-----------
snapshot=17
#-----------
time=14093650211
mem_heap_B=143057972
mem_heap_extra_B=154372
mem_stacks_B=0
heap_tree=empty
#-----------
snapshot=18
#-----------
time=27955232979
mem_heap_B=281470004
mem_heap_extra_B=154372
mem_stacks_B=0
heap_tree=empty
#-----------
snapshot=19
#-----------
time=36356694653
mem_heap_B=281470069
mem_heap_extra_B=154395
mem_stacks_B=0
heap_tree=empty
#-----------
snapshot=20
#-----------
time=36410854720
mem_heap_B=282010675
mem_heap_extra_B=154381
mem_stacks_B=0
heap_tree=empty
#-----------
snapshot=21
#-----------
time=36465020920
mem_heap_B=282551347
mem_heap_extra_B=154381
mem_stacks_B=0
heap_tree=empty
#-----------
snapshot=22
#-----------
time=36573355190
mem_heap_B=283632691
mem_heap_extra_B=154381
mem_stacks_B=0
heap_tree=empty
#-----------
snapshot=23
#-----------
time=36790023642
mem_heap_B=285795379
mem_heap_extra_B=158413
mem_stacks_B=0
heap_tree=empty
#-----------
snapshot=24
#-----------
time=37223360458
mem_heap_B=290120755
mem_heap_extra_B=158413
mem_stacks_B=0
heap_tree=empty
#-----------
snapshot=25
#-----------
time=38090034002
mem_heap_B=298771507
mem_heap_extra_B=158413
mem_stacks_B=0
heap_tree=empty
#-----------
snapshot=26
#-----------
time=39823381002
mem_heap_B=316073011
mem_heap_extra_B=158413
mem_stacks_B=0
heap_tree=detailed
n2: 316073011 (heap allocation functions) malloc/new/new[], --alloc-fns, etc.
 n2: 311430621 0x45543E6: llvm::SmallVectorBase<unsigned long>::grow_pod(void*, unsigned long, unsigned long) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
  n1: 311427201 0x7700FA3: llvm::MCObjectStreamer::emitBytes(llvm::StringRef) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
   n2: 311427201 0x770E4E4: llvm::MCStreamer::emitIntValue(unsigned long, unsigned int) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
    n1: 311427070 0x4BA8889: emitGlobalConstantFP(llvm::APFloat, llvm::Type*, llvm::AsmPrinter&) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
     n1: 311427070 0x4BBE25B: emitGlobalConstantImpl(llvm::DataLayout const&, llvm::Constant const*, llvm::AsmPrinter&, llvm::Constant const*, unsigned long, llvm::DenseMap<unsigned long, llvm::SmallVector<llvm::GlobalAlias const*, 1u>, llvm::DenseMapInfo<unsigned long, void>, llvm::detail::DenseMapPair<unsigned long, llvm::SmallVector<llvm::GlobalAlias const*, 1u> > >*) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
      n1: 311427070 0x4BBEBD5: emitGlobalConstantImpl(llvm::DataLayout const&, llvm::Constant const*, llvm::AsmPrinter&, llvm::Constant const*, unsigned long, llvm::DenseMap<unsigned long, llvm::SmallVector<llvm::GlobalAlias const*, 1u>, llvm::DenseMapInfo<unsigned long, void>, llvm::detail::DenseMapPair<unsigned long, llvm::SmallVector<llvm::GlobalAlias const*, 1u> > >*) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
       n1: 311427070 0x4BBFD1B: llvm::AsmPrinter::emitGlobalVariable(llvm::GlobalVariable const*) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
        n1: 311427070 0x4BBBAC0: llvm::AsmPrinter::doFinalization(llvm::Module&) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
         n1: 311427070 0x7806D84: llvm::FPPassManager::doFinalization(llvm::Module&) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
          n1: 311427070 0x7811305: llvm::legacy::PassManagerImpl::run(llvm::Module&) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
           n1: 311427070 0x5144B05: llvm::orc::SimpleCompiler::operator()(llvm::Module&) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
            n1: 311427070 0x517AD3C: llvm::orc::IRCompileLayer::emit(std::unique_ptr<llvm::orc::MaterializationResponsibility, std::default_delete<llvm::orc::MaterializationResponsibility> >, llvm::orc::ThreadSafeModule) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
             n1: 311427070 0x51B0088: llvm::orc::IRTransformLayer::emit(std::unique_ptr<llvm::orc::MaterializationResponsibility, std::default_delete<llvm::orc::MaterializationResponsibility> >, llvm::orc::ThreadSafeModule) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
              n1: 311427070 0x51B0088: llvm::orc::IRTransformLayer::emit(std::unique_ptr<llvm::orc::MaterializationResponsibility, std::default_delete<llvm::orc::MaterializationResponsibility> >, llvm::orc::ThreadSafeModule) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
               n1: 311427070 0x517FFB5: llvm::orc::BasicIRLayerMaterializationUnit::materialize(std::unique_ptr<llvm::orc::MaterializationResponsibility, std::default_delete<llvm::orc::MaterializationResponsibility> >) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
                n1: 311427070 0x516363F: llvm::orc::MaterializationTask::run() (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
                 n1: 311427070 0x514864C: llvm::orc::ExecutionSession::dispatchTask(std::unique_ptr<llvm::orc::Task, std::default_delete<llvm::orc::Task> >) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
                  n1: 311427070 0x516399B: llvm::orc::ExecutionSession::dispatchOutstandingMUs() (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
                   n1: 311427070 0x5164EA4: llvm::orc::ExecutionSession::OL_completeLookup(std::unique_ptr<llvm::orc::InProgressLookupState, std::default_delete<llvm::orc::InProgressLookupState> >, std::shared_ptr<llvm::orc::AsynchronousSymbolQuery>, std::function<void (llvm::DenseMap<llvm::orc::JITDylib*, llvm::DenseSet<llvm::orc::SymbolStringPtr, llvm::DenseMapInfo<llvm::orc::SymbolStringPtr, void> >, llvm::DenseMapInfo<llvm::orc::JITDylib*, void>, llvm::detail::DenseMapPair<llvm::orc::JITDylib*, llvm::DenseSet<llvm::orc::SymbolStringPtr, llvm::DenseMapInfo<llvm::orc::SymbolStringPtr, void> > > > const&)>) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
                    n1: 311427070 0x5165C73: llvm::orc::InProgressFullLookupState::complete(std::unique_ptr<llvm::orc::InProgressLookupState, std::default_delete<llvm::orc::InProgressLookupState> >) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
                     n1: 311427070 0x515A75E: llvm::orc::ExecutionSession::OL_applyQueryPhase1(std::unique_ptr<llvm::orc::InProgressLookupState, std::default_delete<llvm::orc::InProgressLookupState> >, llvm::Error) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
                      n1: 311427070 0x5166052: llvm::orc::ExecutionSession::lookup(llvm::orc::LookupKind, std::vector<std::pair<llvm::orc::JITDylib*, llvm::orc::JITDylibLookupFlags>, std::allocator<std::pair<llvm::orc::JITDylib*, llvm::orc::JITDylibLookupFlags> > > const&, llvm::orc::SymbolLookupSet, llvm::orc::SymbolState, llvm::unique_function<void (llvm::Expected<llvm::DenseMap<llvm::orc::SymbolStringPtr, llvm::orc::ExecutorSymbolDef, llvm::DenseMapInfo<llvm::orc::SymbolStringPtr, void>, llvm::detail::DenseMapPair<llvm::orc::SymbolStringPtr, llvm::orc::ExecutorSymbolDef> > >)>, std::function<void (llvm::DenseMap<llvm::orc::JITDylib*, llvm::DenseSet<llvm::orc::SymbolStringPtr, llvm::DenseMapInfo<llvm::orc::SymbolStringPtr, void> >, llvm::DenseMapInfo<llvm::orc::JITDylib*, void>, llvm::detail::DenseMapPair<llvm::orc::JITDylib*, llvm::DenseSet<llvm::orc::SymbolStringPtr, llvm::DenseMapInfo<llvm::orc::SymbolStringPtr, void> > > > const&)>) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
                       n1: 311427070 0x5166327: llvm::orc::ExecutionSession::lookup(std::vector<std::pair<llvm::orc::JITDylib*, llvm::orc::JITDylibLookupFlags>, std::allocator<std::pair<llvm::orc::JITDylib*, llvm::orc::JITDylibLookupFlags> > > const&, llvm::orc::SymbolLookupSet, llvm::orc::LookupKind, llvm::orc::SymbolState, std::function<void (llvm::DenseMap<llvm::orc::JITDylib*, llvm::DenseSet<llvm::orc::SymbolStringPtr, llvm::DenseMapInfo<llvm::orc::SymbolStringPtr, void> >, llvm::DenseMapInfo<llvm::orc::JITDylib*, void>, llvm::detail::DenseMapPair<llvm::orc::JITDylib*, llvm::DenseSet<llvm::orc::SymbolStringPtr, llvm::DenseMapInfo<llvm::orc::SymbolStringPtr, void> > > > const&)>) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
                        n1: 311427070 0x516671D: llvm::orc::ExecutionSession::lookup(std::vector<std::pair<llvm::orc::JITDylib*, llvm::orc::JITDylibLookupFlags>, std::allocator<std::pair<llvm::orc::JITDylib*, llvm::orc::JITDylibLookupFlags> > > const&, llvm::orc::SymbolStringPtr, llvm::orc::SymbolState) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
                         n1: 311427070 0x5189AB9: llvm::orc::LLJIT::lookupLinkerMangled(llvm::orc::JITDylib&, llvm::orc::SymbolStringPtr) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
                          n1: 311427070 0x4B9C824: mlir::ExecutionEngine::lookup(llvm::StringRef) const (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
                           n1: 311427070 0x4B9CF78: mlir::ExecutionEngine::lookupPacked(llvm::StringRef) const (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
                            n1: 311427070 0x4B6F974: compileAndExecute((anonymous namespace)::Options&, mlir::Operation*, llvm::StringRef, (anonymous namespace)::CompileAndExecuteConfig, void**, std::unique_ptr<llvm::TargetMachine, std::default_delete<llvm::TargetMachine> >) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
                             n1: 311427070 0x4B6FF5C: compileAndExecuteVoidFunction((anonymous namespace)::Options&, mlir::Operation*, llvm::StringRef, (anonymous namespace)::CompileAndExecuteConfig, std::unique_ptr<llvm::TargetMachine, std::default_delete<llvm::TargetMachine> >) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
                              n0: 311427070 0x4B715A2: mlir::JitRunnerMain(int, char**, mlir::DialectRegistry const&, mlir::JitRunnerConfig) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
    n0: 131 in 1 place, below massif's threshold (1.00%)
  n0: 3420 in 9 places, all below massif's threshold (1.00%)
 n0: 4642390 in 2210 places, all below massif's threshold (1.00%)
#-----------
snapshot=27
#-----------
time=43290074914
mem_heap_B=350676019
mem_heap_extra_B=158413
mem_stacks_B=0
heap_tree=empty
#-----------
snapshot=28
#-----------
time=50223462650
mem_heap_B=419882035
mem_heap_extra_B=158413
mem_stacks_B=0
heap_tree=empty
#-----------
snapshot=29
#-----------
time=64090238034
mem_heap_B=558294067
mem_heap_extra_B=158413
mem_stacks_B=0
heap_tree=empty
#-----------
snapshot=30
#-----------
time=72494848097
mem_heap_B=558294132
mem_heap_extra_B=158436
mem_stacks_B=0
heap_tree=empty
#-----------
snapshot=31
#-----------
time=72548940589
mem_heap_B=558834738
mem_heap_extra_B=158422
mem_stacks_B=0
heap_tree=empty
#-----------
snapshot=32
#-----------
time=72603039205
mem_heap_B=559375410
mem_heap_extra_B=158422
mem_stacks_B=0
heap_tree=detailed
n2: 559375410 (heap allocation functions) malloc/new/new[], --alloc-fns, etc.
 n2: 554733020 0x45543E6: llvm::SmallVectorBase<unsigned long>::grow_pod(void*, unsigned long, unsigned long) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
  n1: 554729600 0x7700FA3: llvm::MCObjectStreamer::emitBytes(llvm::StringRef) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
   n2: 554729600 0x770E4E4: llvm::MCStreamer::emitIntValue(unsigned long, unsigned int) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
    n1: 554729469 0x4BA8889: emitGlobalConstantFP(llvm::APFloat, llvm::Type*, llvm::AsmPrinter&) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
     n1: 554729469 0x4BBE25B: emitGlobalConstantImpl(llvm::DataLayout const&, llvm::Constant const*, llvm::AsmPrinter&, llvm::Constant const*, unsigned long, llvm::DenseMap<unsigned long, llvm::SmallVector<llvm::GlobalAlias const*, 1u>, llvm::DenseMapInfo<unsigned long, void>, llvm::detail::DenseMapPair<unsigned long, llvm::SmallVector<llvm::GlobalAlias const*, 1u> > >*) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
      n1: 554729469 0x4BBEBD5: emitGlobalConstantImpl(llvm::DataLayout const&, llvm::Constant const*, llvm::AsmPrinter&, llvm::Constant const*, unsigned long, llvm::DenseMap<unsigned long, llvm::SmallVector<llvm::GlobalAlias const*, 1u>, llvm::DenseMapInfo<unsigned long, void>, llvm::detail::DenseMapPair<unsigned long, llvm::SmallVector<llvm::GlobalAlias const*, 1u> > >*) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
       n1: 554729469 0x4BBFD1B: llvm::AsmPrinter::emitGlobalVariable(llvm::GlobalVariable const*) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
        n1: 554729469 0x4BBBAC0: llvm::AsmPrinter::doFinalization(llvm::Module&) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
         n1: 554729469 0x7806D84: llvm::FPPassManager::doFinalization(llvm::Module&) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
          n1: 554729469 0x7811305: llvm::legacy::PassManagerImpl::run(llvm::Module&) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
           n1: 554729469 0x5144B05: llvm::orc::SimpleCompiler::operator()(llvm::Module&) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
            n1: 554729469 0x517AD3C: llvm::orc::IRCompileLayer::emit(std::unique_ptr<llvm::orc::MaterializationResponsibility, std::default_delete<llvm::orc::MaterializationResponsibility> >, llvm::orc::ThreadSafeModule) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
             n1: 554729469 0x51B0088: llvm::orc::IRTransformLayer::emit(std::unique_ptr<llvm::orc::MaterializationResponsibility, std::default_delete<llvm::orc::MaterializationResponsibility> >, llvm::orc::ThreadSafeModule) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
              n1: 554729469 0x51B0088: llvm::orc::IRTransformLayer::emit(std::unique_ptr<llvm::orc::MaterializationResponsibility, std::default_delete<llvm::orc::MaterializationResponsibility> >, llvm::orc::ThreadSafeModule) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
               n1: 554729469 0x517FFB5: llvm::orc::BasicIRLayerMaterializationUnit::materialize(std::unique_ptr<llvm::orc::MaterializationResponsibility, std::default_delete<llvm::orc::MaterializationResponsibility> >) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
                n1: 554729469 0x516363F: llvm::orc::MaterializationTask::run() (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
                 n1: 554729469 0x514864C: llvm::orc::ExecutionSession::dispatchTask(std::unique_ptr<llvm::orc::Task, std::default_delete<llvm::orc::Task> >) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
                  n1: 554729469 0x516399B: llvm::orc::ExecutionSession::dispatchOutstandingMUs() (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
                   n1: 554729469 0x5164EA4: llvm::orc::ExecutionSession::OL_completeLookup(std::unique_ptr<llvm::orc::InProgressLookupState, std::default_delete<llvm::orc::InProgressLookupState> >, std::shared_ptr<llvm::orc::AsynchronousSymbolQuery>, std::function<void (llvm::DenseMap<llvm::orc::JITDylib*, llvm::DenseSet<llvm::orc::SymbolStringPtr, llvm::DenseMapInfo<llvm::orc::SymbolStringPtr, void> >, llvm::DenseMapInfo<llvm::orc::JITDylib*, void>, llvm::detail::DenseMapPair<llvm::orc::JITDylib*, llvm::DenseSet<llvm::orc::SymbolStringPtr, llvm::DenseMapInfo<llvm::orc::SymbolStringPtr, void> > > > const&)>) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
                    n1: 554729469 0x5165C73: llvm::orc::InProgressFullLookupState::complete(std::unique_ptr<llvm::orc::InProgressLookupState, std::default_delete<llvm::orc::InProgressLookupState> >) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
                     n1: 554729469 0x515A75E: llvm::orc::ExecutionSession::OL_applyQueryPhase1(std::unique_ptr<llvm::orc::InProgressLookupState, std::default_delete<llvm::orc::InProgressLookupState> >, llvm::Error) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
                      n1: 554729469 0x5166052: llvm::orc::ExecutionSession::lookup(llvm::orc::LookupKind, std::vector<std::pair<llvm::orc::JITDylib*, llvm::orc::JITDylibLookupFlags>, std::allocator<std::pair<llvm::orc::JITDylib*, llvm::orc::JITDylibLookupFlags> > > const&, llvm::orc::SymbolLookupSet, llvm::orc::SymbolState, llvm::unique_function<void (llvm::Expected<llvm::DenseMap<llvm::orc::SymbolStringPtr, llvm::orc::ExecutorSymbolDef, llvm::DenseMapInfo<llvm::orc::SymbolStringPtr, void>, llvm::detail::DenseMapPair<llvm::orc::SymbolStringPtr, llvm::orc::ExecutorSymbolDef> > >)>, std::function<void (llvm::DenseMap<llvm::orc::JITDylib*, llvm::DenseSet<llvm::orc::SymbolStringPtr, llvm::DenseMapInfo<llvm::orc::SymbolStringPtr, void> >, llvm::DenseMapInfo<llvm::orc::JITDylib*, void>, llvm::detail::DenseMapPair<llvm::orc::JITDylib*, llvm::DenseSet<llvm::orc::SymbolStringPtr, llvm::DenseMapInfo<llvm::orc::SymbolStringPtr, void> > > > const&)>) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
                       n1: 554729469 0x5166327: llvm::orc::ExecutionSession::lookup(std::vector<std::pair<llvm::orc::JITDylib*, llvm::orc::JITDylibLookupFlags>, std::allocator<std::pair<llvm::orc::JITDylib*, llvm::orc::JITDylibLookupFlags> > > const&, llvm::orc::SymbolLookupSet, llvm::orc::LookupKind, llvm::orc::SymbolState, std::function<void (llvm::DenseMap<llvm::orc::JITDylib*, llvm::DenseSet<llvm::orc::SymbolStringPtr, llvm::DenseMapInfo<llvm::orc::SymbolStringPtr, void> >, llvm::DenseMapInfo<llvm::orc::JITDylib*, void>, llvm::detail::DenseMapPair<llvm::orc::JITDylib*, llvm::DenseSet<llvm::orc::SymbolStringPtr, llvm::DenseMapInfo<llvm::orc::SymbolStringPtr, void> > > > const&)>) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
                        n1: 554729469 0x516671D: llvm::orc::ExecutionSession::lookup(std::vector<std::pair<llvm::orc::JITDylib*, llvm::orc::JITDylibLookupFlags>, std::allocator<std::pair<llvm::orc::JITDylib*, llvm::orc::JITDylibLookupFlags> > > const&, llvm::orc::SymbolStringPtr, llvm::orc::SymbolState) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
                         n1: 554729469 0x5189AB9: llvm::orc::LLJIT::lookupLinkerMangled(llvm::orc::JITDylib&, llvm::orc::SymbolStringPtr) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
                          n1: 554729469 0x4B9C824: mlir::ExecutionEngine::lookup(llvm::StringRef) const (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
                           n1: 554729469 0x4B9CF78: mlir::ExecutionEngine::lookupPacked(llvm::StringRef) const (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
                            n1: 554729469 0x4B6F974: compileAndExecute((anonymous namespace)::Options&, mlir::Operation*, llvm::StringRef, (anonymous namespace)::CompileAndExecuteConfig, void**, std::unique_ptr<llvm::TargetMachine, std::default_delete<llvm::TargetMachine> >) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
                             n1: 554729469 0x4B6FF5C: compileAndExecuteVoidFunction((anonymous namespace)::Options&, mlir::Operation*, llvm::StringRef, (anonymous namespace)::CompileAndExecuteConfig, std::unique_ptr<llvm::TargetMachine, std::default_delete<llvm::TargetMachine> >) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
                              n0: 554729469 0x4B715A2: mlir::JitRunnerMain(int, char**, mlir::DialectRegistry const&, mlir::JitRunnerConfig) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
    n0: 131 in 1 place, below massif's threshold (1.00%)
  n0: 3420 in 9 places, all below massif's threshold (1.00%)
 n0: 4642390 in 2210 places, all below massif's threshold (1.00%)
#-----------
snapshot=33
#-----------
time=72711238307
mem_heap_B=560456754
mem_heap_extra_B=158422
mem_stacks_B=0
heap_tree=empty
#-----------
snapshot=34
#-----------
time=72927636423
mem_heap_B=562619442
mem_heap_extra_B=162454
mem_stacks_B=0
heap_tree=empty
#-----------
snapshot=35
#-----------
time=73360432567
mem_heap_B=566944818
mem_heap_extra_B=162454
mem_stacks_B=0
heap_tree=empty
#-----------
snapshot=36
#-----------
time=74226024767
mem_heap_B=575595570
mem_heap_extra_B=162454
mem_stacks_B=0
heap_tree=empty
#-----------
snapshot=37
#-----------
time=75957209079
mem_heap_B=592897074
mem_heap_extra_B=162454
mem_stacks_B=0
heap_tree=empty
#-----------
snapshot=38
#-----------
time=79419577615
mem_heap_B=627500082
mem_heap_extra_B=162454
mem_stacks_B=0
heap_tree=empty
#-----------
snapshot=39
#-----------
time=86344314599
mem_heap_B=696706098
mem_heap_extra_B=162454
mem_stacks_B=0
heap_tree=empty
#-----------
snapshot=40
#-----------
time=100193788479
mem_heap_B=835118130
mem_heap_extra_B=162454
mem_stacks_B=0
heap_tree=empty
#-----------
snapshot=41
#-----------
time=108587425753
mem_heap_B=835118195
mem_heap_extra_B=162477
mem_stacks_B=0
heap_tree=empty
#-----------
snapshot=42
#-----------
time=108617768072
mem_heap_B=835405360
mem_heap_extra_B=162472
mem_stacks_B=0
heap_tree=empty
#-----------
snapshot=43
#-----------
time=108644817488
mem_heap_B=835675696
mem_heap_extra_B=162472
mem_stacks_B=0
heap_tree=empty
#-----------
snapshot=44
#-----------
time=108698916232
mem_heap_B=836216368
mem_heap_extra_B=162472
mem_stacks_B=0
heap_tree=empty
#-----------
snapshot=45
#-----------
time=108721899346
mem_heap_B=836220464
mem_heap_extra_B=162480
mem_stacks_B=0
heap_tree=empty
#-----------
snapshot=46
#-----------
time=108753856006
mem_heap_B=1557701364
mem_heap_extra_B=163388
mem_stacks_B=0
heap_tree=empty
#-----------
snapshot=47
#-----------
time=108785534088
mem_heap_B=1557701523
mem_heap_extra_B=163405
mem_stacks_B=0
heap_tree=detailed
n2: 1557701523 (heap allocation functions) malloc/new/new[], --alloc-fns, etc.
 n3: 1553014244 0x45543E6: llvm::SmallVectorBase<unsigned long>::grow_pod(void*, unsigned long, unsigned long) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
  n1: 831570821 0x7700FA3: llvm::MCObjectStreamer::emitBytes(llvm::StringRef) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
   n2: 831570821 0x770E4E4: llvm::MCStreamer::emitIntValue(unsigned long, unsigned int) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
    n1: 831570690 0x4BA8889: emitGlobalConstantFP(llvm::APFloat, llvm::Type*, llvm::AsmPrinter&) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
     n2: 831570690 0x4BBE25B: emitGlobalConstantImpl(llvm::DataLayout const&, llvm::Constant const*, llvm::AsmPrinter&, llvm::Constant const*, unsigned long, llvm::DenseMap<unsigned long, llvm::SmallVector<llvm::GlobalAlias const*, 1u>, llvm::DenseMapInfo<unsigned long, void>, llvm::detail::DenseMapPair<unsigned long, llvm::SmallVector<llvm::GlobalAlias const*, 1u> > >*) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
      n2: 831553795 0x4BBEBD5: emitGlobalConstantImpl(llvm::DataLayout const&, llvm::Constant const*, llvm::AsmPrinter&, llvm::Constant const*, unsigned long, llvm::DenseMap<unsigned long, llvm::SmallVector<llvm::GlobalAlias const*, 1u>, llvm::DenseMapInfo<unsigned long, void>, llvm::detail::DenseMapPair<unsigned long, llvm::SmallVector<llvm::GlobalAlias const*, 1u> > >*) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
       n1: 830472189 0x4BBFD1B: llvm::AsmPrinter::emitGlobalVariable(llvm::GlobalVariable const*) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
        n1: 830472189 0x4BBBAC0: llvm::AsmPrinter::doFinalization(llvm::Module&) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
         n1: 830472189 0x7806D84: llvm::FPPassManager::doFinalization(llvm::Module&) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
          n1: 830472189 0x7811305: llvm::legacy::PassManagerImpl::run(llvm::Module&) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
           n1: 830472189 0x5144B05: llvm::orc::SimpleCompiler::operator()(llvm::Module&) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
            n1: 830472189 0x517AD3C: llvm::orc::IRCompileLayer::emit(std::unique_ptr<llvm::orc::MaterializationResponsibility, std::default_delete<llvm::orc::MaterializationResponsibility> >, llvm::orc::ThreadSafeModule) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
             n1: 830472189 0x51B0088: llvm::orc::IRTransformLayer::emit(std::unique_ptr<llvm::orc::MaterializationResponsibility, std::default_delete<llvm::orc::MaterializationResponsibility> >, llvm::orc::ThreadSafeModule) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
              n1: 830472189 0x51B0088: llvm::orc::IRTransformLayer::emit(std::unique_ptr<llvm::orc::MaterializationResponsibility, std::default_delete<llvm::orc::MaterializationResponsibility> >, llvm::orc::ThreadSafeModule) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
               n1: 830472189 0x517FFB5: llvm::orc::BasicIRLayerMaterializationUnit::materialize(std::unique_ptr<llvm::orc::MaterializationResponsibility, std::default_delete<llvm::orc::MaterializationResponsibility> >) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
                n1: 830472189 0x516363F: llvm::orc::MaterializationTask::run() (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
                 n1: 830472189 0x514864C: llvm::orc::ExecutionSession::dispatchTask(std::unique_ptr<llvm::orc::Task, std::default_delete<llvm::orc::Task> >) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
                  n1: 830472189 0x516399B: llvm::orc::ExecutionSession::dispatchOutstandingMUs() (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
                   n1: 830472189 0x5164EA4: llvm::orc::ExecutionSession::OL_completeLookup(std::unique_ptr<llvm::orc::InProgressLookupState, std::default_delete<llvm::orc::InProgressLookupState> >, std::shared_ptr<llvm::orc::AsynchronousSymbolQuery>, std::function<void (llvm::DenseMap<llvm::orc::JITDylib*, llvm::DenseSet<llvm::orc::SymbolStringPtr, llvm::DenseMapInfo<llvm::orc::SymbolStringPtr, void> >, llvm::DenseMapInfo<llvm::orc::JITDylib*, void>, llvm::detail::DenseMapPair<llvm::orc::JITDylib*, llvm::DenseSet<llvm::orc::SymbolStringPtr, llvm::DenseMapInfo<llvm::orc::SymbolStringPtr, void> > > > const&)>) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
                    n1: 830472189 0x5165C73: llvm::orc::InProgressFullLookupState::complete(std::unique_ptr<llvm::orc::InProgressLookupState, std::default_delete<llvm::orc::InProgressLookupState> >) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
                     n1: 830472189 0x515A75E: llvm::orc::ExecutionSession::OL_applyQueryPhase1(std::unique_ptr<llvm::orc::InProgressLookupState, std::default_delete<llvm::orc::InProgressLookupState> >, llvm::Error) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
                      n1: 830472189 0x5166052: llvm::orc::ExecutionSession::lookup(llvm::orc::LookupKind, std::vector<std::pair<llvm::orc::JITDylib*, llvm::orc::JITDylibLookupFlags>, std::allocator<std::pair<llvm::orc::JITDylib*, llvm::orc::JITDylibLookupFlags> > > const&, llvm::orc::SymbolLookupSet, llvm::orc::SymbolState, llvm::unique_function<void (llvm::Expected<llvm::DenseMap<llvm::orc::SymbolStringPtr, llvm::orc::ExecutorSymbolDef, llvm::DenseMapInfo<llvm::orc::SymbolStringPtr, void>, llvm::detail::DenseMapPair<llvm::orc::SymbolStringPtr, llvm::orc::ExecutorSymbolDef> > >)>, std::function<void (llvm::DenseMap<llvm::orc::JITDylib*, llvm::DenseSet<llvm::orc::SymbolStringPtr, llvm::DenseMapInfo<llvm::orc::SymbolStringPtr, void> >, llvm::DenseMapInfo<llvm::orc::JITDylib*, void>, llvm::detail::DenseMapPair<llvm::orc::JITDylib*, llvm::DenseSet<llvm::orc::SymbolStringPtr, llvm::DenseMapInfo<llvm::orc::SymbolStringPtr, void> > > > const&)>) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
                       n1: 830472189 0x5166327: llvm::orc::ExecutionSession::lookup(std::vector<std::pair<llvm::orc::JITDylib*, llvm::orc::JITDylibLookupFlags>, std::allocator<std::pair<llvm::orc::JITDylib*, llvm::orc::JITDylibLookupFlags> > > const&, llvm::orc::SymbolLookupSet, llvm::orc::LookupKind, llvm::orc::SymbolState, std::function<void (llvm::DenseMap<llvm::orc::JITDylib*, llvm::DenseSet<llvm::orc::SymbolStringPtr, llvm::DenseMapInfo<llvm::orc::SymbolStringPtr, void> >, llvm::DenseMapInfo<llvm::orc::JITDylib*, void>, llvm::detail::DenseMapPair<llvm::orc::JITDylib*, llvm::DenseSet<llvm::orc::SymbolStringPtr, llvm::DenseMapInfo<llvm::orc::SymbolStringPtr, void> > > > const&)>) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
                        n1: 830472189 0x516671D: llvm::orc::ExecutionSession::lookup(std::vector<std::pair<llvm::orc::JITDylib*, llvm::orc::JITDylibLookupFlags>, std::allocator<std::pair<llvm::orc::JITDylib*, llvm::orc::JITDylibLookupFlags> > > const&, llvm::orc::SymbolStringPtr, llvm::orc::SymbolState) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
                         n1: 830472189 0x5189AB9: llvm::orc::LLJIT::lookupLinkerMangled(llvm::orc::JITDylib&, llvm::orc::SymbolStringPtr) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
                          n1: 830472189 0x4B9C824: mlir::ExecutionEngine::lookup(llvm::StringRef) const (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
                           n1: 830472189 0x4B9CF78: mlir::ExecutionEngine::lookupPacked(llvm::StringRef) const (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
                            n1: 830472189 0x4B6F974: compileAndExecute((anonymous namespace)::Options&, mlir::Operation*, llvm::StringRef, (anonymous namespace)::CompileAndExecuteConfig, void**, std::unique_ptr<llvm::TargetMachine, std::default_delete<llvm::TargetMachine> >) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
                             n1: 830472189 0x4B6FF5C: compileAndExecuteVoidFunction((anonymous namespace)::Options&, mlir::Operation*, llvm::StringRef, (anonymous namespace)::CompileAndExecuteConfig, std::unique_ptr<llvm::TargetMachine, std::default_delete<llvm::TargetMachine> >) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
                              n0: 830472189 0x4B715A2: mlir::JitRunnerMain(int, char**, mlir::DialectRegistry const&, mlir::JitRunnerConfig) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
       n0: 1081606 in 1 place, below massif's threshold (1.00%)
      n0: 16895 in 1 place, below massif's threshold (1.00%)
    n0: 131 in 1 place, below massif's threshold (1.00%)
  n2: 721440003 0x457B258: llvm::raw_svector_ostream::write_impl(char const*, unsigned long) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
   n2: 721440003 0x457C309: llvm::raw_ostream::write(char const*, unsigned long) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
    n1: 721440003 0x76C923D: llvm::MCAssembler::writeSectionData(llvm::raw_ostream&, llvm::MCSection const*) const (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
     n1: 721440003 0x775AE90: (anonymous namespace)::ELFWriter::writeObject(llvm::MCAssembler&) [clone .constprop.0] (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
      n1: 721440003 0x775D2D8: llvm::ELFObjectWriter::writeObject(llvm::MCAssembler&) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
       n1: 721440003 0x76C8F1E: llvm::MCAssembler::Finish() (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
        n1: 721440003 0x4BBD08B: llvm::AsmPrinter::doFinalization(llvm::Module&) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
         n1: 721440003 0x7806D84: llvm::FPPassManager::doFinalization(llvm::Module&) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
          n1: 721440003 0x7811305: llvm::legacy::PassManagerImpl::run(llvm::Module&) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
           n1: 721440003 0x5144B05: llvm::orc::SimpleCompiler::operator()(llvm::Module&) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
            n1: 721440003 0x517AD3C: llvm::orc::IRCompileLayer::emit(std::unique_ptr<llvm::orc::MaterializationResponsibility, std::default_delete<llvm::orc::MaterializationResponsibility> >, llvm::orc::ThreadSafeModule) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
             n1: 721440003 0x51B0088: llvm::orc::IRTransformLayer::emit(std::unique_ptr<llvm::orc::MaterializationResponsibility, std::default_delete<llvm::orc::MaterializationResponsibility> >, llvm::orc::ThreadSafeModule) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
              n1: 721440003 0x51B0088: llvm::orc::IRTransformLayer::emit(std::unique_ptr<llvm::orc::MaterializationResponsibility, std::default_delete<llvm::orc::MaterializationResponsibility> >, llvm::orc::ThreadSafeModule) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
               n1: 721440003 0x517FFB5: llvm::orc::BasicIRLayerMaterializationUnit::materialize(std::unique_ptr<llvm::orc::MaterializationResponsibility, std::default_delete<llvm::orc::MaterializationResponsibility> >) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
                n1: 721440003 0x516363F: llvm::orc::MaterializationTask::run() (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
                 n1: 721440003 0x514864C: llvm::orc::ExecutionSession::dispatchTask(std::unique_ptr<llvm::orc::Task, std::default_delete<llvm::orc::Task> >) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
                  n1: 721440003 0x516399B: llvm::orc::ExecutionSession::dispatchOutstandingMUs() (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
                   n1: 721440003 0x5164EA4: llvm::orc::ExecutionSession::OL_completeLookup(std::unique_ptr<llvm::orc::InProgressLookupState, std::default_delete<llvm::orc::InProgressLookupState> >, std::shared_ptr<llvm::orc::AsynchronousSymbolQuery>, std::function<void (llvm::DenseMap<llvm::orc::JITDylib*, llvm::DenseSet<llvm::orc::SymbolStringPtr, llvm::DenseMapInfo<llvm::orc::SymbolStringPtr, void> >, llvm::DenseMapInfo<llvm::orc::JITDylib*, void>, llvm::detail::DenseMapPair<llvm::orc::JITDylib*, llvm::DenseSet<llvm::orc::SymbolStringPtr, llvm::DenseMapInfo<llvm::orc::SymbolStringPtr, void> > > > const&)>) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
                    n1: 721440003 0x5165C73: llvm::orc::InProgressFullLookupState::complete(std::unique_ptr<llvm::orc::InProgressLookupState, std::default_delete<llvm::orc::InProgressLookupState> >) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
                     n1: 721440003 0x515A75E: llvm::orc::ExecutionSession::OL_applyQueryPhase1(std::unique_ptr<llvm::orc::InProgressLookupState, std::default_delete<llvm::orc::InProgressLookupState> >, llvm::Error) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
                      n1: 721440003 0x5166052: llvm::orc::ExecutionSession::lookup(llvm::orc::LookupKind, std::vector<std::pair<llvm::orc::JITDylib*, llvm::orc::JITDylibLookupFlags>, std::allocator<std::pair<llvm::orc::JITDylib*, llvm::orc::JITDylibLookupFlags> > > const&, llvm::orc::SymbolLookupSet, llvm::orc::SymbolState, llvm::unique_function<void (llvm::Expected<llvm::DenseMap<llvm::orc::SymbolStringPtr, llvm::orc::ExecutorSymbolDef, llvm::DenseMapInfo<llvm::orc::SymbolStringPtr, void>, llvm::detail::DenseMapPair<llvm::orc::SymbolStringPtr, llvm::orc::ExecutorSymbolDef> > >)>, std::function<void (llvm::DenseMap<llvm::orc::JITDylib*, llvm::DenseSet<llvm::orc::SymbolStringPtr, llvm::DenseMapInfo<llvm::orc::SymbolStringPtr, void> >, llvm::DenseMapInfo<llvm::orc::JITDylib*, void>, llvm::detail::DenseMapPair<llvm::orc::JITDylib*, llvm::DenseSet<llvm::orc::SymbolStringPtr, llvm::DenseMapInfo<llvm::orc::SymbolStringPtr, void> > > > const&)>) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
                       n1: 721440003 0x5166327: llvm::orc::ExecutionSession::lookup(std::vector<std::pair<llvm::orc::JITDylib*, llvm::orc::JITDylibLookupFlags>, std::allocator<std::pair<llvm::orc::JITDylib*, llvm::orc::JITDylibLookupFlags> > > const&, llvm::orc::SymbolLookupSet, llvm::orc::LookupKind, llvm::orc::SymbolState, std::function<void (llvm::DenseMap<llvm::orc::JITDylib*, llvm::DenseSet<llvm::orc::SymbolStringPtr, llvm::DenseMapInfo<llvm::orc::SymbolStringPtr, void> >, llvm::DenseMapInfo<llvm::orc::JITDylib*, void>, llvm::detail::DenseMapPair<llvm::orc::JITDylib*, llvm::DenseSet<llvm::orc::SymbolStringPtr, llvm::DenseMapInfo<llvm::orc::SymbolStringPtr, void> > > > const&)>) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
                        n1: 721440003 0x516671D: llvm::orc::ExecutionSession::lookup(std::vector<std::pair<llvm::orc::JITDylib*, llvm::orc::JITDylibLookupFlags>, std::allocator<std::pair<llvm::orc::JITDylib*, llvm::orc::JITDylibLookupFlags> > > const&, llvm::orc::SymbolStringPtr, llvm::orc::SymbolState) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
                         n1: 721440003 0x5189AB9: llvm::orc::LLJIT::lookupLinkerMangled(llvm::orc::JITDylib&, llvm::orc::SymbolStringPtr) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
                          n1: 721440003 0x4B9C824: mlir::ExecutionEngine::lookup(llvm::StringRef) const (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
                           n1: 721440003 0x4B9CF78: mlir::ExecutionEngine::lookupPacked(llvm::StringRef) const (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
                            n1: 721440003 0x4B6F974: compileAndExecute((anonymous namespace)::Options&, mlir::Operation*, llvm::StringRef, (anonymous namespace)::CompileAndExecuteConfig, void**, std::unique_ptr<llvm::TargetMachine, std::default_delete<llvm::TargetMachine> >) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
                             n1: 721440003 0x4B6FF5C: compileAndExecuteVoidFunction((anonymous namespace)::Options&, mlir::Operation*, llvm::StringRef, (anonymous namespace)::CompileAndExecuteConfig, std::unique_ptr<llvm::TargetMachine, std::default_delete<llvm::TargetMachine> >) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
                              n0: 721440003 0x4B715A2: mlir::JitRunnerMain(int, char**, mlir::DialectRegistry const&, mlir::JitRunnerConfig) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
    n0: 0 in 4 places, all below massif's threshold (1.00%)
   n0: 0 in 1 place, below massif's threshold (1.00%)
  n0: 3420 in 9 places, all below massif's threshold (1.00%)
 n0: 4687279 in 2216 places, all below massif's threshold (1.00%)
#-----------
snapshot=48
#-----------
time=108832675288
mem_heap_B=1269087290
mem_heap_extra_B=140558
mem_stacks_B=0
heap_tree=empty
#-----------
snapshot=49
#-----------
time=108880769642
mem_heap_B=1267922762
mem_heap_extra_B=114262
mem_stacks_B=0
heap_tree=empty
#-----------
snapshot=50
#-----------
time=108928008875
mem_heap_B=1812100849
mem_heap_extra_B=119479
mem_stacks_B=0
heap_tree=empty
#-----------
snapshot=51
#-----------
time=108928017338
mem_heap_B=1812105497
mem_heap_extra_B=119519
mem_stacks_B=0
heap_tree=peak
n3: 1812105497 (heap allocation functions) malloc/new/new[], --alloc-fns, etc.
 n2: 1088323518 0x459A65D: llvm::WritableMemoryBuffer::getNewUninitMemBuffer(unsigned long, llvm::Twine const&, std::optional<llvm::Align>) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
  n2: 1088323518 0x459A959: llvm::MemoryBuffer::getMemBufferCopy(llvm::StringRef, llvm::Twine const&) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
   n1: 544161759 0x4B9795E: mlir::SimpleObjectCache::notifyObjectCompiled(llvm::Module const*, llvm::MemoryBufferRef) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
    n1: 544161759 0x5144D86: llvm::orc::SimpleCompiler::operator()(llvm::Module&) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
     n1: 544161759 0x517AD3C: llvm::orc::IRCompileLayer::emit(std::unique_ptr<llvm::orc::MaterializationResponsibility, std::default_delete<llvm::orc::MaterializationResponsibility> >, llvm::orc::ThreadSafeModule) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
      n1: 544161759 0x51B0088: llvm::orc::IRTransformLayer::emit(std::unique_ptr<llvm::orc::MaterializationResponsibility, std::default_delete<llvm::orc::MaterializationResponsibility> >, llvm::orc::ThreadSafeModule) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
       n1: 544161759 0x51B0088: llvm::orc::IRTransformLayer::emit(std::unique_ptr<llvm::orc::MaterializationResponsibility, std::default_delete<llvm::orc::MaterializationResponsibility> >, llvm::orc::ThreadSafeModule) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
        n1: 544161759 0x517FFB5: llvm::orc::BasicIRLayerMaterializationUnit::materialize(std::unique_ptr<llvm::orc::MaterializationResponsibility, std::default_delete<llvm::orc::MaterializationResponsibility> >) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
         n1: 544161759 0x516363F: llvm::orc::MaterializationTask::run() (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
          n1: 544161759 0x514864C: llvm::orc::ExecutionSession::dispatchTask(std::unique_ptr<llvm::orc::Task, std::default_delete<llvm::orc::Task> >) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
           n1: 544161759 0x516399B: llvm::orc::ExecutionSession::dispatchOutstandingMUs() (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
            n1: 544161759 0x5164EA4: llvm::orc::ExecutionSession::OL_completeLookup(std::unique_ptr<llvm::orc::InProgressLookupState, std::default_delete<llvm::orc::InProgressLookupState> >, std::shared_ptr<llvm::orc::AsynchronousSymbolQuery>, std::function<void (llvm::DenseMap<llvm::orc::JITDylib*, llvm::DenseSet<llvm::orc::SymbolStringPtr, llvm::DenseMapInfo<llvm::orc::SymbolStringPtr, void> >, llvm::DenseMapInfo<llvm::orc::JITDylib*, void>, llvm::detail::DenseMapPair<llvm::orc::JITDylib*, llvm::DenseSet<llvm::orc::SymbolStringPtr, llvm::DenseMapInfo<llvm::orc::SymbolStringPtr, void> > > > const&)>) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
             n1: 544161759 0x5165C73: llvm::orc::InProgressFullLookupState::complete(std::unique_ptr<llvm::orc::InProgressLookupState, std::default_delete<llvm::orc::InProgressLookupState> >) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
              n1: 544161759 0x515A75E: llvm::orc::ExecutionSession::OL_applyQueryPhase1(std::unique_ptr<llvm::orc::InProgressLookupState, std::default_delete<llvm::orc::InProgressLookupState> >, llvm::Error) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
               n1: 544161759 0x5166052: llvm::orc::ExecutionSession::lookup(llvm::orc::LookupKind, std::vector<std::pair<llvm::orc::JITDylib*, llvm::orc::JITDylibLookupFlags>, std::allocator<std::pair<llvm::orc::JITDylib*, llvm::orc::JITDylibLookupFlags> > > const&, llvm::orc::SymbolLookupSet, llvm::orc::SymbolState, llvm::unique_function<void (llvm::Expected<llvm::DenseMap<llvm::orc::SymbolStringPtr, llvm::orc::ExecutorSymbolDef, llvm::DenseMapInfo<llvm::orc::SymbolStringPtr, void>, llvm::detail::DenseMapPair<llvm::orc::SymbolStringPtr, llvm::orc::ExecutorSymbolDef> > >)>, std::function<void (llvm::DenseMap<llvm::orc::JITDylib*, llvm::DenseSet<llvm::orc::SymbolStringPtr, llvm::DenseMapInfo<llvm::orc::SymbolStringPtr, void> >, llvm::DenseMapInfo<llvm::orc::JITDylib*, void>, llvm::detail::DenseMapPair<llvm::orc::JITDylib*, llvm::DenseSet<llvm::orc::SymbolStringPtr, llvm::DenseMapInfo<llvm::orc::SymbolStringPtr, void> > > > const&)>) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
                n1: 544161759 0x5166327: llvm::orc::ExecutionSession::lookup(std::vector<std::pair<llvm::orc::JITDylib*, llvm::orc::JITDylibLookupFlags>, std::allocator<std::pair<llvm::orc::JITDylib*, llvm::orc::JITDylibLookupFlags> > > const&, llvm::orc::SymbolLookupSet, llvm::orc::LookupKind, llvm::orc::SymbolState, std::function<void (llvm::DenseMap<llvm::orc::JITDylib*, llvm::DenseSet<llvm::orc::SymbolStringPtr, llvm::DenseMapInfo<llvm::orc::SymbolStringPtr, void> >, llvm::DenseMapInfo<llvm::orc::JITDylib*, void>, llvm::detail::DenseMapPair<llvm::orc::JITDylib*, llvm::DenseSet<llvm::orc::SymbolStringPtr, llvm::DenseMapInfo<llvm::orc::SymbolStringPtr, void> > > > const&)>) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
                 n1: 544161759 0x516671D: llvm::orc::ExecutionSession::lookup(std::vector<std::pair<llvm::orc::JITDylib*, llvm::orc::JITDylibLookupFlags>, std::allocator<std::pair<llvm::orc::JITDylib*, llvm::orc::JITDylibLookupFlags> > > const&, llvm::orc::SymbolStringPtr, llvm::orc::SymbolState) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
                  n1: 544161759 0x5189AB9: llvm::orc::LLJIT::lookupLinkerMangled(llvm::orc::JITDylib&, llvm::orc::SymbolStringPtr) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
                   n1: 544161759 0x4B9C824: mlir::ExecutionEngine::lookup(llvm::StringRef) const (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
                    n1: 544161759 0x4B9CF78: mlir::ExecutionEngine::lookupPacked(llvm::StringRef) const (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
                     n1: 544161759 0x4B6F974: compileAndExecute((anonymous namespace)::Options&, mlir::Operation*, llvm::StringRef, (anonymous namespace)::CompileAndExecuteConfig, void**, std::unique_ptr<llvm::TargetMachine, std::default_delete<llvm::TargetMachine> >) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
                      n1: 544161759 0x4B6FF5C: compileAndExecuteVoidFunction((anonymous namespace)::Options&, mlir::Operation*, llvm::StringRef, (anonymous namespace)::CompileAndExecuteConfig, std::unique_ptr<llvm::TargetMachine, std::default_delete<llvm::TargetMachine> >) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
                       n1: 544161759 0x4B715A2: mlir::JitRunnerMain(int, char**, mlir::DialectRegistry const&, mlir::JitRunnerConfig) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
                        n0: 544161759 0x42B4749: main (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
   n1: 544161759 0x51EDAFE: (anonymous namespace)::createELFDebugObject(llvm::object::ObjectFile const&, (anonymous namespace)::LoadedELFObjectInfo const&) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
    n1: 544161759 0x51EE93A: (anonymous namespace)::LoadedELFObjectInfo::getObjectForDebug(llvm::object::ObjectFile const&) const (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
     n1: 544161759 0x51BC8EB: (anonymous namespace)::GDBJITRegistrationListener::notifyObjectLoaded(unsigned long, llvm::object::ObjectFile const&, llvm::RuntimeDyld::LoadedObjectInfo const&) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
      n1: 544161759 0x51A30BD: llvm::orc::RTDyldObjectLinkingLayer::onObjEmit(llvm::orc::MaterializationResponsibility&, llvm::object::OwningBinary<llvm::object::ObjectFile>, std::unique_ptr<llvm::RuntimeDyld::MemoryManager, std::default_delete<llvm::RuntimeDyld::MemoryManager> >, std::unique_ptr<llvm::RuntimeDyld::LoadedObjectInfo, std::default_delete<llvm::RuntimeDyld::LoadedObjectInfo> >, std::unique_ptr<llvm::DenseMap<llvm::orc::JITDylib*, llvm::DenseSet<llvm::orc::SymbolStringPtr, llvm::DenseMapInfo<llvm::orc::SymbolStringPtr, void> >, llvm::DenseMapInfo<llvm::orc::JITDylib*, void>, llvm::detail::DenseMapPair<llvm::orc::JITDylib*, llvm::DenseSet<llvm::orc::SymbolStringPtr, llvm::DenseMapInfo<llvm::orc::SymbolStringPtr, void> > > >, std::default_delete<llvm::DenseMap<llvm::orc::JITDylib*, llvm::DenseSet<llvm::orc::SymbolStringPtr, llvm::DenseMapInfo<llvm::orc::SymbolStringPtr, void> >, llvm::DenseMapInfo<llvm::orc::JITDylib*, void>, llvm::detail::DenseMapPair<llvm::orc::JITDylib*, llvm::DenseSet<llvm::orc::SymbolStringPtr, llvm::DenseMapInfo<llvm::orc::SymbolStringPtr, void> > > > > >, llvm::Error) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
       n1: 544161759 0x51A350F: void llvm::detail::UniqueFunctionBase<void, llvm::object::OwningBinary<llvm::object::ObjectFile>, std::unique_ptr<llvm::RuntimeDyld::LoadedObjectInfo, std::default_delete<llvm::RuntimeDyld::LoadedObjectInfo> >, llvm::Error>::CallImpl<llvm::orc::RTDyldObjectLinkingLayer::emit(std::unique_ptr<llvm::orc::MaterializationResponsibility, std::default_delete<llvm::orc::MaterializationResponsibility> >, std::unique_ptr<llvm::MemoryBuffer, std::default_delete<llvm::MemoryBuffer> >)::{lambda(llvm::object::OwningBinary<llvm::object::ObjectFile>, std::unique_ptr<llvm::RuntimeDyld::LoadedObjectInfo, std::default_delete<llvm::RuntimeDyld::LoadedObjectInfo> >, llvm::Error)#2}>(void*, llvm::object::OwningBinary<llvm::object::ObjectFile>&, std::unique_ptr<llvm::RuntimeDyld::LoadedObjectInfo, std::default_delete<llvm::RuntimeDyld::LoadedObjectInfo> >&, llvm::Error&) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
        n1: 544161759 0x51C9632: llvm::RuntimeDyldImpl::finalizeAsync(std::unique_ptr<llvm::RuntimeDyldImpl, std::default_delete<llvm::RuntimeDyldImpl> >, llvm::unique_function<void (llvm::object::OwningBinary<llvm::object::ObjectFile>, std::unique_ptr<llvm::RuntimeDyld::LoadedObjectInfo, std::default_delete<llvm::RuntimeDyld::LoadedObjectInfo> >, llvm::Error)>, llvm::object::OwningBinary<llvm::object::ObjectFile>, std::unique_ptr<llvm::RuntimeDyld::LoadedObjectInfo, std::default_delete<llvm::RuntimeDyld::LoadedObjectInfo> >)::{lambda(llvm::Expected<std::map<llvm::StringRef, llvm::JITEvaluatedSymbol, std::less<llvm::StringRef>, std::allocator<std::pair<llvm::StringRef const, llvm::JITEvaluatedSymbol> > > >)#1}::operator()(llvm::Expected<std::map<llvm::StringRef, llvm::JITEvaluatedSymbol, std::less<llvm::StringRef>, std::allocator<std::pair<llvm::StringRef const, llvm::JITEvaluatedSymbol> > > >) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
         n1: 544161759 0x51C97F9: void llvm::detail::UniqueFunctionBase<void, llvm::Expected<std::map<llvm::StringRef, llvm::JITEvaluatedSymbol, std::less<llvm::StringRef>, std::allocator<std::pair<llvm::StringRef const, llvm::JITEvaluatedSymbol> > > > >::CallImpl<llvm::RuntimeDyldImpl::finalizeAsync(std::unique_ptr<llvm::RuntimeDyldImpl, std::default_delete<llvm::RuntimeDyldImpl> >, llvm::unique_function<void (llvm::object::OwningBinary<llvm::object::ObjectFile>, std::unique_ptr<llvm::RuntimeDyld::LoadedObjectInfo, std::default_delete<llvm::RuntimeDyld::LoadedObjectInfo> >, llvm::Error)>, llvm::object::OwningBinary<llvm::object::ObjectFile>, std::unique_ptr<llvm::RuntimeDyld::LoadedObjectInfo, std::default_delete<llvm::RuntimeDyld::LoadedObjectInfo> >)::{lambda(llvm::Expected<std::map<llvm::StringRef, llvm::JITEvaluatedSymbol, std::less<llvm::StringRef>, std::allocator<std::pair<llvm::StringRef const, llvm::JITEvaluatedSymbol> > > >)#1}>(void*, llvm::Expected<std::map<llvm::StringRef, llvm::JITEvaluatedSymbol, std::less<llvm::StringRef>, std::allocator<std::pair<llvm::StringRef const, llvm::JITEvaluatedSymbol> > > >&) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
          n1: 544161759 0x519E08D: void llvm::detail::UniqueFunctionBase<void, llvm::Expected<llvm::DenseMap<llvm::orc::SymbolStringPtr, llvm::orc::ExecutorSymbolDef, llvm::DenseMapInfo<llvm::orc::SymbolStringPtr, void>, llvm::detail::DenseMapPair<llvm::orc::SymbolStringPtr, llvm::orc::ExecutorSymbolDef> > > >::CallImpl<(anonymous namespace)::JITDylibSearchOrderResolver::lookup(std::set<llvm::StringRef, std::less<llvm::StringRef>, std::allocator<llvm::StringRef> > const&, llvm::unique_function<void (llvm::Expected<std::map<llvm::StringRef, llvm::JITEvaluatedSymbol, std::less<llvm::StringRef>, std::allocator<std::pair<llvm::StringRef const, llvm::JITEvaluatedSymbol> > > >)>)::{lambda(llvm::Expected<llvm::DenseMap<llvm::orc::SymbolStringPtr, llvm::orc::ExecutorSymbolDef, llvm::DenseMapInfo<llvm::orc::SymbolStringPtr, void>, llvm::detail::DenseMapPair<llvm::orc::SymbolStringPtr, llvm::orc::ExecutorSymbolDef> > >)#1}>(void*, llvm::Expected<llvm::DenseMap<llvm::orc::SymbolStringPtr, llvm::orc::ExecutorSymbolDef, llvm::DenseMapInfo<llvm::orc::SymbolStringPtr, void>, llvm::detail::DenseMapPair<llvm::orc::SymbolStringPtr, llvm::orc::ExecutorSymbolDef> > >&) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
           n1: 544161759 0x514FD63: llvm::orc::AsynchronousSymbolQuery::handleComplete(llvm::orc::ExecutionSession&)::RunQueryCompleteTask::run() (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
            n1: 544161759 0x514864C: llvm::orc::ExecutionSession::dispatchTask(std::unique_ptr<llvm::orc::Task, std::default_delete<llvm::orc::Task> >) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
             n1: 544161759 0x514954C: llvm::orc::AsynchronousSymbolQuery::handleComplete(llvm::orc::ExecutionSession&) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
              n1: 544161759 0x515CEA7: llvm::orc::JITDylib::resolve(llvm::orc::MaterializationResponsibility&, llvm::DenseMap<llvm::orc::SymbolStringPtr, llvm::orc::ExecutorSymbolDef, llvm::DenseMapInfo<llvm::orc::SymbolStringPtr, void>, llvm::detail::DenseMapPair<llvm::orc::SymbolStringPtr, llvm::orc::ExecutorSymbolDef> > const&) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
               n1: 544161759 0x515D700: llvm::orc::ExecutionSession::OL_notifyResolved(llvm::orc::MaterializationResponsibility&, llvm::DenseMap<llvm::orc::SymbolStringPtr, llvm::orc::ExecutorSymbolDef, llvm::DenseMapInfo<llvm::orc::SymbolStringPtr, void>, llvm::detail::DenseMapPair<llvm::orc::SymbolStringPtr, llvm::orc::ExecutorSymbolDef> > const&) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
                n1: 544161759 0x5142F59: llvm::orc::AbsoluteSymbolsMaterializationUnit::materialize(std::unique_ptr<llvm::orc::MaterializationResponsibility, std::default_delete<llvm::orc::MaterializationResponsibility> >) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
                 n1: 544161759 0x516363F: llvm::orc::MaterializationTask::run() (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
                  n1: 544161759 0x514864C: llvm::orc::ExecutionSession::dispatchTask(std::unique_ptr<llvm::orc::Task, std::default_delete<llvm::orc::Task> >) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
                   n1: 544161759 0x516399B: llvm::orc::ExecutionSession::dispatchOutstandingMUs() (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
                    n1: 544161759 0x5164EA4: llvm::orc::ExecutionSession::OL_completeLookup(std::unique_ptr<llvm::orc::InProgressLookupState, std::default_delete<llvm::orc::InProgressLookupState> >, std::shared_ptr<llvm::orc::AsynchronousSymbolQuery>, std::function<void (llvm::DenseMap<llvm::orc::JITDylib*, llvm::DenseSet<llvm::orc::SymbolStringPtr, llvm::DenseMapInfo<llvm::orc::SymbolStringPtr, void> >, llvm::DenseMapInfo<llvm::orc::JITDylib*, void>, llvm::detail::DenseMapPair<llvm::orc::JITDylib*, llvm::DenseSet<llvm::orc::SymbolStringPtr, llvm::DenseMapInfo<llvm::orc::SymbolStringPtr, void> > > > const&)>) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
                     n1: 544161759 0x5165C73: llvm::orc::InProgressFullLookupState::complete(std::unique_ptr<llvm::orc::InProgressLookupState, std::default_delete<llvm::orc::InProgressLookupState> >) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
                      n1: 544161759 0x515A75E: llvm::orc::ExecutionSession::OL_applyQueryPhase1(std::unique_ptr<llvm::orc::InProgressLookupState, std::default_delete<llvm::orc::InProgressLookupState> >, llvm::Error) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
                       n1: 544161759 0x5166052: llvm::orc::ExecutionSession::lookup(llvm::orc::LookupKind, std::vector<std::pair<llvm::orc::JITDylib*, llvm::orc::JITDylibLookupFlags>, std::allocator<std::pair<llvm::orc::JITDylib*, llvm::orc::JITDylibLookupFlags> > > const&, llvm::orc::SymbolLookupSet, llvm::orc::SymbolState, llvm::unique_function<void (llvm::Expected<llvm::DenseMap<llvm::orc::SymbolStringPtr, llvm::orc::ExecutorSymbolDef, llvm::DenseMapInfo<llvm::orc::SymbolStringPtr, void>, llvm::detail::DenseMapPair<llvm::orc::SymbolStringPtr, llvm::orc::ExecutorSymbolDef> > >)>, std::function<void (llvm::DenseMap<llvm::orc::JITDylib*, llvm::DenseSet<llvm::orc::SymbolStringPtr, llvm::DenseMapInfo<llvm::orc::SymbolStringPtr, void> >, llvm::DenseMapInfo<llvm::orc::JITDylib*, void>, llvm::detail::DenseMapPair<llvm::orc::JITDylib*, llvm::DenseSet<llvm::orc::SymbolStringPtr, llvm::DenseMapInfo<llvm::orc::SymbolStringPtr, void> > > > const&)>) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
                        n1: 544161759 0x519C8B1: (anonymous namespace)::JITDylibSearchOrderResolver::lookup(std::set<llvm::StringRef, std::less<llvm::StringRef>, std::allocator<llvm::StringRef> > const&, llvm::unique_function<void (llvm::Expected<std::map<llvm::StringRef, llvm::JITEvaluatedSymbol, std::less<llvm::StringRef>, std::allocator<std::pair<llvm::StringRef const, llvm::JITEvaluatedSymbol> > > >)>) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
                         n1: 544161759 0x51C9E6E: llvm::RuntimeDyldImpl::finalizeAsync(std::unique_ptr<llvm::RuntimeDyldImpl, std::default_delete<llvm::RuntimeDyldImpl> >, llvm::unique_function<void (llvm::object::OwningBinary<llvm::object::ObjectFile>, std::unique_ptr<llvm::RuntimeDyld::LoadedObjectInfo, std::default_delete<llvm::RuntimeDyld::LoadedObjectInfo> >, llvm::Error)>, llvm::object::OwningBinary<llvm::object::ObjectFile>, std::unique_ptr<llvm::RuntimeDyld::LoadedObjectInfo, std::default_delete<llvm::RuntimeDyld::LoadedObjectInfo> >) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
                          n1: 544161759 0x51CA65C: llvm::jitLinkForORC(llvm::object::OwningBinary<llvm::object::ObjectFile>, llvm::RuntimeDyld::MemoryManager&, llvm::JITSymbolResolver&, bool, llvm::unique_function<llvm::Error (llvm::object::ObjectFile const&, llvm::RuntimeDyld::LoadedObjectInfo&, std::map<llvm::StringRef, llvm::JITEvaluatedSymbol, std::less<llvm::StringRef>, std::allocator<std::pair<llvm::StringRef const, llvm::JITEvaluatedSymbol> > >)>, llvm::unique_function<void (llvm::object::OwningBinary<llvm::object::ObjectFile>, std::unique_ptr<llvm::RuntimeDyld::LoadedObjectInfo, std::default_delete<llvm::RuntimeDyld::LoadedObjectInfo> >, llvm::Error)>) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
                           n1: 544161759 0x519F0AA: llvm::orc::RTDyldObjectLinkingLayer::emit(std::unique_ptr<llvm::orc::MaterializationResponsibility, std::default_delete<llvm::orc::MaterializationResponsibility> >, std::unique_ptr<llvm::MemoryBuffer, std::default_delete<llvm::MemoryBuffer> >) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
                            n1: 544161759 0x5199B45: llvm::orc::ObjectTransformLayer::emit(std::unique_ptr<llvm::orc::MaterializationResponsibility, std::default_delete<llvm::orc::MaterializationResponsibility> >, std::unique_ptr<llvm::MemoryBuffer, std::default_delete<llvm::MemoryBuffer> >) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
                             n1: 544161759 0x517AF3E: llvm::orc::IRCompileLayer::emit(std::unique_ptr<llvm::orc::MaterializationResponsibility, std::default_delete<llvm::orc::MaterializationResponsibility> >, llvm::orc::ThreadSafeModule) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
                              n0: 544161759 0x51B0088: llvm::orc::IRTransformLayer::emit(std::unique_ptr<llvm::orc::MaterializationResponsibility, std::default_delete<llvm::orc::MaterializationResponsibility> >, llvm::orc::ThreadSafeModule) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
  n0: 0 in 1 place, below massif's threshold (1.00%)
 n2: 721440003 0x45543E6: llvm::SmallVectorBase<unsigned long>::grow_pod(void*, unsigned long, unsigned long) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
  n2: 721440003 0x457B258: llvm::raw_svector_ostream::write_impl(char const*, unsigned long) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
   n2: 721440003 0x457C309: llvm::raw_ostream::write(char const*, unsigned long) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
    n1: 721440003 0x76C923D: llvm::MCAssembler::writeSectionData(llvm::raw_ostream&, llvm::MCSection const*) const (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
     n1: 721440003 0x775AE90: (anonymous namespace)::ELFWriter::writeObject(llvm::MCAssembler&) [clone .constprop.0] (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
      n1: 721440003 0x775D2D8: llvm::ELFObjectWriter::writeObject(llvm::MCAssembler&) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
       n1: 721440003 0x76C8F1E: llvm::MCAssembler::Finish() (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
        n1: 721440003 0x4BBD08B: llvm::AsmPrinter::doFinalization(llvm::Module&) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
         n1: 721440003 0x7806D84: llvm::FPPassManager::doFinalization(llvm::Module&) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
          n1: 721440003 0x7811305: llvm::legacy::PassManagerImpl::run(llvm::Module&) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
           n1: 721440003 0x5144B05: llvm::orc::SimpleCompiler::operator()(llvm::Module&) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
            n1: 721440003 0x517AD3C: llvm::orc::IRCompileLayer::emit(std::unique_ptr<llvm::orc::MaterializationResponsibility, std::default_delete<llvm::orc::MaterializationResponsibility> >, llvm::orc::ThreadSafeModule) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
             n1: 721440003 0x51B0088: llvm::orc::IRTransformLayer::emit(std::unique_ptr<llvm::orc::MaterializationResponsibility, std::default_delete<llvm::orc::MaterializationResponsibility> >, llvm::orc::ThreadSafeModule) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
              n1: 721440003 0x51B0088: llvm::orc::IRTransformLayer::emit(std::unique_ptr<llvm::orc::MaterializationResponsibility, std::default_delete<llvm::orc::MaterializationResponsibility> >, llvm::orc::ThreadSafeModule) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
               n1: 721440003 0x517FFB5: llvm::orc::BasicIRLayerMaterializationUnit::materialize(std::unique_ptr<llvm::orc::MaterializationResponsibility, std::default_delete<llvm::orc::MaterializationResponsibility> >) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
                n1: 721440003 0x516363F: llvm::orc::MaterializationTask::run() (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
                 n1: 721440003 0x514864C: llvm::orc::ExecutionSession::dispatchTask(std::unique_ptr<llvm::orc::Task, std::default_delete<llvm::orc::Task> >) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
                  n1: 721440003 0x516399B: llvm::orc::ExecutionSession::dispatchOutstandingMUs() (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
                   n1: 721440003 0x5164EA4: llvm::orc::ExecutionSession::OL_completeLookup(std::unique_ptr<llvm::orc::InProgressLookupState, std::default_delete<llvm::orc::InProgressLookupState> >, std::shared_ptr<llvm::orc::AsynchronousSymbolQuery>, std::function<void (llvm::DenseMap<llvm::orc::JITDylib*, llvm::DenseSet<llvm::orc::SymbolStringPtr, llvm::DenseMapInfo<llvm::orc::SymbolStringPtr, void> >, llvm::DenseMapInfo<llvm::orc::JITDylib*, void>, llvm::detail::DenseMapPair<llvm::orc::JITDylib*, llvm::DenseSet<llvm::orc::SymbolStringPtr, llvm::DenseMapInfo<llvm::orc::SymbolStringPtr, void> > > > const&)>) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
                    n1: 721440003 0x5165C73: llvm::orc::InProgressFullLookupState::complete(std::unique_ptr<llvm::orc::InProgressLookupState, std::default_delete<llvm::orc::InProgressLookupState> >) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
                     n1: 721440003 0x515A75E: llvm::orc::ExecutionSession::OL_applyQueryPhase1(std::unique_ptr<llvm::orc::InProgressLookupState, std::default_delete<llvm::orc::InProgressLookupState> >, llvm::Error) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
                      n1: 721440003 0x5166052: llvm::orc::ExecutionSession::lookup(llvm::orc::LookupKind, std::vector<std::pair<llvm::orc::JITDylib*, llvm::orc::JITDylibLookupFlags>, std::allocator<std::pair<llvm::orc::JITDylib*, llvm::orc::JITDylibLookupFlags> > > const&, llvm::orc::SymbolLookupSet, llvm::orc::SymbolState, llvm::unique_function<void (llvm::Expected<llvm::DenseMap<llvm::orc::SymbolStringPtr, llvm::orc::ExecutorSymbolDef, llvm::DenseMapInfo<llvm::orc::SymbolStringPtr, void>, llvm::detail::DenseMapPair<llvm::orc::SymbolStringPtr, llvm::orc::ExecutorSymbolDef> > >)>, std::function<void (llvm::DenseMap<llvm::orc::JITDylib*, llvm::DenseSet<llvm::orc::SymbolStringPtr, llvm::DenseMapInfo<llvm::orc::SymbolStringPtr, void> >, llvm::DenseMapInfo<llvm::orc::JITDylib*, void>, llvm::detail::DenseMapPair<llvm::orc::JITDylib*, llvm::DenseSet<llvm::orc::SymbolStringPtr, llvm::DenseMapInfo<llvm::orc::SymbolStringPtr, void> > > > const&)>) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
                       n1: 721440003 0x5166327: llvm::orc::ExecutionSession::lookup(std::vector<std::pair<llvm::orc::JITDylib*, llvm::orc::JITDylibLookupFlags>, std::allocator<std::pair<llvm::orc::JITDylib*, llvm::orc::JITDylibLookupFlags> > > const&, llvm::orc::SymbolLookupSet, llvm::orc::LookupKind, llvm::orc::SymbolState, std::function<void (llvm::DenseMap<llvm::orc::JITDylib*, llvm::DenseSet<llvm::orc::SymbolStringPtr, llvm::DenseMapInfo<llvm::orc::SymbolStringPtr, void> >, llvm::DenseMapInfo<llvm::orc::JITDylib*, void>, llvm::detail::DenseMapPair<llvm::orc::JITDylib*, llvm::DenseSet<llvm::orc::SymbolStringPtr, llvm::DenseMapInfo<llvm::orc::SymbolStringPtr, void> > > > const&)>) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
                        n1: 721440003 0x516671D: llvm::orc::ExecutionSession::lookup(std::vector<std::pair<llvm::orc::JITDylib*, llvm::orc::JITDylibLookupFlags>, std::allocator<std::pair<llvm::orc::JITDylib*, llvm::orc::JITDylibLookupFlags> > > const&, llvm::orc::SymbolStringPtr, llvm::orc::SymbolState) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
                         n1: 721440003 0x5189AB9: llvm::orc::LLJIT::lookupLinkerMangled(llvm::orc::JITDylib&, llvm::orc::SymbolStringPtr) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
                          n1: 721440003 0x4B9C824: mlir::ExecutionEngine::lookup(llvm::StringRef) const (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
                           n1: 721440003 0x4B9CF78: mlir::ExecutionEngine::lookupPacked(llvm::StringRef) const (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
                            n1: 721440003 0x4B6F974: compileAndExecute((anonymous namespace)::Options&, mlir::Operation*, llvm::StringRef, (anonymous namespace)::CompileAndExecuteConfig, void**, std::unique_ptr<llvm::TargetMachine, std::default_delete<llvm::TargetMachine> >) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
                             n1: 721440003 0x4B6FF5C: compileAndExecuteVoidFunction((anonymous namespace)::Options&, mlir::Operation*, llvm::StringRef, (anonymous namespace)::CompileAndExecuteConfig, std::unique_ptr<llvm::TargetMachine, std::default_delete<llvm::TargetMachine> >) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
                              n0: 721440003 0x4B715A2: mlir::JitRunnerMain(int, char**, mlir::DialectRegistry const&, mlir::JitRunnerConfig) (in /home/<USER>/Personal_Projects/OSPP/buddy-mlir/llvm/build/bin/mlir-runner)
    n0: 0 in 4 places, all below massif's threshold (1.00%)
   n0: 0 in 1 place, below massif's threshold (1.00%)
  n0: 0 in 10 places, all below massif's threshold (1.00%)
 n0: 2341976 in 2258 places, all below massif's threshold (1.00%)
#-----------
snapshot=52
#-----------
time=109294893626
mem_heap_B=1274713028
mem_heap_extra_B=121220
mem_stacks_B=0
heap_tree=empty
#-----------
snapshot=53
#-----------
time=110127118349
mem_heap_B=1276474372
mem_heap_extra_B=121228
mem_stacks_B=0
heap_tree=empty
#-----------
snapshot=54
#-----------
time=110506367620
mem_heap_B=1458590852
mem_heap_extra_B=125212
mem_stacks_B=0
heap_tree=empty
#-----------
snapshot=55
#-----------
time=111338592342
mem_heap_B=1460352196
mem_heap_extra_B=125220
mem_stacks_B=0
heap_tree=empty
#-----------
snapshot=56
#-----------
time=111702066038
mem_heap_B=1641362756
mem_heap_extra_B=129204
mem_stacks_B=0
heap_tree=empty
#-----------
snapshot=57
#-----------
time=112535455430
mem_heap_B=1642018180
mem_heap_extra_B=129212
mem_stacks_B=0
heap_tree=empty
#-----------
snapshot=58
#-----------
time=113315053547
mem_heap_B=1642019235
mem_heap_extra_B=129245
mem_stacks_B=0
heap_tree=empty
