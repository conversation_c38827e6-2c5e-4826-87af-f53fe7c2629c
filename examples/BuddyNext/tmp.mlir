module {
  llvm.func @malloc(i64) -> !llvm.ptr
  llvm.func @printNewline()
  llvm.func @printF64(f64)
  llvm.mlir.global private constant @__constant_4096x11008xf32(dense<6.000000e+00> : tensor<4096x11008xf32>) {addr_space = 0 : i32, alignment = 64 : i64} : !llvm.array<4096 x array<11008 x f32>>
  llvm.mlir.global private constant @__constant_11008x4096xf32_0(dense<5.000000e+00> : tensor<11008x4096xf32>) {addr_space = 0 : i32, alignment = 64 : i64} : !llvm.array<11008 x array<4096 x f32>>
  llvm.mlir.global private constant @__constant_11008x4096xf32(dense<4.000000e+00> : tensor<11008x4096xf32>) {addr_space = 0 : i32, alignment = 64 : i64} : !llvm.array<11008 x array<4096 x f32>>
  llvm.mlir.global private constant @__constant_4096xf32(dense<3.000000e+00> : tensor<4096xf32>) {addr_space = 0 : i32, alignment = 64 : i64} : !llvm.array<4096 x f32>
  llvm.mlir.global private constant @__constant_1x40x4096xf32(dense<2.000000e+00> : tensor<1x40x4096xf32>) {addr_space = 0 : i32, alignment = 64 : i64} : !llvm.array<1 x array<40 x array<4096 x f32>>>
  llvm.mlir.global private constant @__constant_1x1x1xf32(dense<2.44140625E-4> : tensor<1x1x1xf32>) {addr_space = 0 : i32, alignment = 64 : i64} : !llvm.array<1 x array<1 x array<1 x f32>>>
  llvm.mlir.global private constant @__constant_1x40x1xf32(dense<9.99999974E-6> : tensor<1x40x1xf32>) {addr_space = 0 : i32, alignment = 64 : i64} : !llvm.array<1 x array<40 x array<1 x f32>>>
  llvm.mlir.global private constant @__constant_40x11008xf32(dense<0.000000e+00> : tensor<40x11008xf32>) {addr_space = 0 : i32, alignment = 64 : i64} : !llvm.array<40 x array<11008 x f32>>
  llvm.mlir.global private constant @__constant_40x4096xf32(dense<0.000000e+00> : tensor<40x4096xf32>) {addr_space = 0 : i32, alignment = 64 : i64} : !llvm.array<40 x array<4096 x f32>>
  llvm.func @rtclock() -> f64 attributes {sym_visibility = "private"}
  llvm.func @printMemrefF32(i64, !llvm.ptr) attributes {sym_visibility = "private"}
  llvm.func @kernel_optimized(%arg0: !llvm.ptr, %arg1: !llvm.ptr, %arg2: i64, %arg3: i64, %arg4: i64, %arg5: i64, %arg6: i64, %arg7: i64, %arg8: i64, %arg9: !llvm.ptr, %arg10: !llvm.ptr, %arg11: i64, %arg12: i64, %arg13: i64, %arg14: !llvm.ptr, %arg15: !llvm.ptr, %arg16: i64, %arg17: i64, %arg18: i64, %arg19: i64, %arg20: i64, %arg21: !llvm.ptr, %arg22: !llvm.ptr, %arg23: i64, %arg24: i64, %arg25: i64, %arg26: i64, %arg27: i64, %arg28: !llvm.ptr, %arg29: !llvm.ptr, %arg30: i64, %arg31: i64, %arg32: i64, %arg33: i64, %arg34: i64) {
    %0 = llvm.mlir.undef : !llvm.struct<(ptr, ptr, i64, array<2 x i64>, array<2 x i64>)>
    %1 = llvm.insertvalue %arg28, %0[0] : !llvm.struct<(ptr, ptr, i64, array<2 x i64>, array<2 x i64>)> 
    %2 = llvm.insertvalue %arg29, %1[1] : !llvm.struct<(ptr, ptr, i64, array<2 x i64>, array<2 x i64>)> 
    %3 = llvm.insertvalue %arg30, %2[2] : !llvm.struct<(ptr, ptr, i64, array<2 x i64>, array<2 x i64>)> 
    %4 = llvm.insertvalue %arg31, %3[3, 0] : !llvm.struct<(ptr, ptr, i64, array<2 x i64>, array<2 x i64>)> 
    %5 = llvm.insertvalue %arg33, %4[4, 0] : !llvm.struct<(ptr, ptr, i64, array<2 x i64>, array<2 x i64>)> 
    %6 = llvm.insertvalue %arg32, %5[3, 1] : !llvm.struct<(ptr, ptr, i64, array<2 x i64>, array<2 x i64>)> 
    %7 = llvm.insertvalue %arg34, %6[4, 1] : !llvm.struct<(ptr, ptr, i64, array<2 x i64>, array<2 x i64>)> 
    %8 = llvm.mlir.undef : !llvm.struct<(ptr, ptr, i64, array<2 x i64>, array<2 x i64>)>
    %9 = llvm.insertvalue %arg21, %8[0] : !llvm.struct<(ptr, ptr, i64, array<2 x i64>, array<2 x i64>)> 
    %10 = llvm.insertvalue %arg22, %9[1] : !llvm.struct<(ptr, ptr, i64, array<2 x i64>, array<2 x i64>)> 
    %11 = llvm.insertvalue %arg23, %10[2] : !llvm.struct<(ptr, ptr, i64, array<2 x i64>, array<2 x i64>)> 
    %12 = llvm.insertvalue %arg24, %11[3, 0] : !llvm.struct<(ptr, ptr, i64, array<2 x i64>, array<2 x i64>)> 
    %13 = llvm.insertvalue %arg26, %12[4, 0] : !llvm.struct<(ptr, ptr, i64, array<2 x i64>, array<2 x i64>)> 
    %14 = llvm.insertvalue %arg25, %13[3, 1] : !llvm.struct<(ptr, ptr, i64, array<2 x i64>, array<2 x i64>)> 
    %15 = llvm.insertvalue %arg27, %14[4, 1] : !llvm.struct<(ptr, ptr, i64, array<2 x i64>, array<2 x i64>)> 
    %16 = llvm.mlir.undef : !llvm.struct<(ptr, ptr, i64, array<2 x i64>, array<2 x i64>)>
    %17 = llvm.insertvalue %arg14, %16[0] : !llvm.struct<(ptr, ptr, i64, array<2 x i64>, array<2 x i64>)> 
    %18 = llvm.insertvalue %arg15, %17[1] : !llvm.struct<(ptr, ptr, i64, array<2 x i64>, array<2 x i64>)> 
    %19 = llvm.insertvalue %arg16, %18[2] : !llvm.struct<(ptr, ptr, i64, array<2 x i64>, array<2 x i64>)> 
    %20 = llvm.insertvalue %arg17, %19[3, 0] : !llvm.struct<(ptr, ptr, i64, array<2 x i64>, array<2 x i64>)> 
    %21 = llvm.insertvalue %arg19, %20[4, 0] : !llvm.struct<(ptr, ptr, i64, array<2 x i64>, array<2 x i64>)> 
    %22 = llvm.insertvalue %arg18, %21[3, 1] : !llvm.struct<(ptr, ptr, i64, array<2 x i64>, array<2 x i64>)> 
    %23 = llvm.insertvalue %arg20, %22[4, 1] : !llvm.struct<(ptr, ptr, i64, array<2 x i64>, array<2 x i64>)> 
    %24 = llvm.mlir.undef : !llvm.struct<(ptr, ptr, i64, array<1 x i64>, array<1 x i64>)>
    %25 = llvm.insertvalue %arg9, %24[0] : !llvm.struct<(ptr, ptr, i64, array<1 x i64>, array<1 x i64>)> 
    %26 = llvm.insertvalue %arg10, %25[1] : !llvm.struct<(ptr, ptr, i64, array<1 x i64>, array<1 x i64>)> 
    %27 = llvm.insertvalue %arg11, %26[2] : !llvm.struct<(ptr, ptr, i64, array<1 x i64>, array<1 x i64>)> 
    %28 = llvm.insertvalue %arg12, %27[3, 0] : !llvm.struct<(ptr, ptr, i64, array<1 x i64>, array<1 x i64>)> 
    %29 = llvm.insertvalue %arg13, %28[4, 0] : !llvm.struct<(ptr, ptr, i64, array<1 x i64>, array<1 x i64>)> 
    %30 = llvm.mlir.undef : !llvm.struct<(ptr, ptr, i64, array<3 x i64>, array<3 x i64>)>
    %31 = llvm.insertvalue %arg0, %30[0] : !llvm.struct<(ptr, ptr, i64, array<3 x i64>, array<3 x i64>)> 
    %32 = llvm.insertvalue %arg1, %31[1] : !llvm.struct<(ptr, ptr, i64, array<3 x i64>, array<3 x i64>)> 
    %33 = llvm.insertvalue %arg2, %32[2] : !llvm.struct<(ptr, ptr, i64, array<3 x i64>, array<3 x i64>)> 
    %34 = llvm.insertvalue %arg3, %33[3, 0] : !llvm.struct<(ptr, ptr, i64, array<3 x i64>, array<3 x i64>)> 
    %35 = llvm.insertvalue %arg6, %34[4, 0] : !llvm.struct<(ptr, ptr, i64, array<3 x i64>, array<3 x i64>)> 
    %36 = llvm.insertvalue %arg4, %35[3, 1] : !llvm.struct<(ptr, ptr, i64, array<3 x i64>, array<3 x i64>)> 
    %37 = llvm.insertvalue %arg7, %36[4, 1] : !llvm.struct<(ptr, ptr, i64, array<3 x i64>, array<3 x i64>)> 
    %38 = llvm.insertvalue %arg5, %37[3, 2] : !llvm.struct<(ptr, ptr, i64, array<3 x i64>, array<3 x i64>)> 
    %39 = llvm.insertvalue %arg8, %38[4, 2] : !llvm.struct<(ptr, ptr, i64, array<3 x i64>, array<3 x i64>)> 
    %40 = llvm.mlir.undef : !llvm.struct<(i64, ptr)>
    %41 = llvm.mlir.constant(3 : index) : i64
    %42 = llvm.mlir.constant(0 : i32) : i32
    %43 = llvm.mlir.undef : vector<64xf32>
    %44 = llvm.mlir.undef : !llvm.struct<(ptr, ptr, i64, array<3 x i64>, array<3 x i64>)>
    %45 = llvm.mlir.addressof @__constant_40x11008xf32 : !llvm.ptr
    %46 = llvm.mlir.constant(440320 : index) : i64
    %47 = llvm.mlir.addressof @__constant_40x4096xf32 : !llvm.ptr
    %48 = llvm.mlir.zero : !llvm.ptr
    %49 = llvm.mlir.constant(163840 : index) : i64
    %50 = llvm.mlir.constant(1.000000e+00 : f32) : f32
    %51 = llvm.mlir.constant(0.000000e+00 : f32) : f32
    %52 = llvm.mlir.constant(2 : i32) : i32
    %53 = llvm.mlir.constant(40 : index) : i64
    %54 = llvm.mlir.constant(4096 : index) : i64
    %55 = llvm.mlir.constant(11008 : index) : i64
    %56 = llvm.mlir.constant(2.44140625E-4 : f32) : f32
    %57 = llvm.mlir.constant(9.99999974E-6 : f32) : f32
    %58 = llvm.mlir.constant(0 : index) : i64
    %59 = llvm.mlir.constant(1 : index) : i64
    %60 = llvm.mlir.constant(172 : index) : i64
    %61 = llvm.mlir.constant(64 : index) : i64
    %62 = llvm.getelementptr %47[0, 0, 0] : (!llvm.ptr) -> !llvm.ptr, !llvm.array<40 x array<4096 x f32>>
    %63 = llvm.getelementptr %45[0, 0, 0] : (!llvm.ptr) -> !llvm.ptr, !llvm.array<40 x array<11008 x f32>>
    %64 = llvm.call @rtclock() : () -> f64
    %65 = llvm.getelementptr %48[163840] : (!llvm.ptr) -> !llvm.ptr, f32
    %66 = llvm.ptrtoint %65 : !llvm.ptr to i64
    %67 = llvm.add %66, %61 : i64
    %68 = llvm.call @malloc(%67) : (i64) -> !llvm.ptr
    %69 = llvm.ptrtoint %68 : !llvm.ptr to i64
    %70 = llvm.sub %61, %59 : i64
    %71 = llvm.add %69, %70 : i64
    %72 = llvm.urem %71, %61 : i64
    %73 = llvm.sub %71, %72 : i64
    %74 = llvm.inttoptr %73 : i64 to !llvm.ptr
    llvm.br ^bb1(%58 : i64)
  ^bb1(%75: i64):  // 2 preds: ^bb0, ^bb8
    %76 = llvm.icmp "slt" %75, %59 : i64
    llvm.cond_br %76, ^bb2, ^bb9
  ^bb2:  // pred: ^bb1
    llvm.br ^bb3(%58 : i64)
  ^bb3(%77: i64):  // 2 preds: ^bb2, ^bb7
    %78 = llvm.icmp "slt" %77, %53 : i64
    llvm.cond_br %78, ^bb4, ^bb8
  ^bb4:  // pred: ^bb3
    llvm.br ^bb5(%58 : i64)
  ^bb5(%79: i64):  // 2 preds: ^bb4, ^bb6
    %80 = llvm.icmp "slt" %79, %54 : i64
    llvm.cond_br %80, ^bb6, ^bb7
  ^bb6:  // pred: ^bb5
    %81 = llvm.extractvalue %39[1] : !llvm.struct<(ptr, ptr, i64, array<3 x i64>, array<3 x i64>)> 
    %82 = llvm.extractvalue %39[2] : !llvm.struct<(ptr, ptr, i64, array<3 x i64>, array<3 x i64>)> 
    %83 = llvm.getelementptr %81[%82] : (!llvm.ptr, i64) -> !llvm.ptr, f32
    %84 = llvm.extractvalue %39[4, 0] : !llvm.struct<(ptr, ptr, i64, array<3 x i64>, array<3 x i64>)> 
    %85 = llvm.mul %75, %84 : i64
    %86 = llvm.extractvalue %39[4, 1] : !llvm.struct<(ptr, ptr, i64, array<3 x i64>, array<3 x i64>)> 
    %87 = llvm.mul %77, %86 : i64
    %88 = llvm.add %85, %87 : i64
    %89 = llvm.extractvalue %39[4, 2] : !llvm.struct<(ptr, ptr, i64, array<3 x i64>, array<3 x i64>)> 
    %90 = llvm.mul %79, %89 : i64
    %91 = llvm.add %88, %90 : i64
    %92 = llvm.getelementptr %83[%91] : (!llvm.ptr, i64) -> !llvm.ptr, f32
    %93 = llvm.load %92 : !llvm.ptr -> f32
    %94 = llvm.intr.powi(%93, %52) : (f32, i32) -> f32
    %95 = llvm.mul %75, %49 : i64
    %96 = llvm.mul %77, %54 : i64
    %97 = llvm.add %95, %96 : i64
    %98 = llvm.add %97, %79 : i64
    %99 = llvm.getelementptr %74[%98] : (!llvm.ptr, i64) -> !llvm.ptr, f32
    llvm.store %94, %99 : f32, !llvm.ptr
    %100 = llvm.add %79, %59 : i64
    llvm.br ^bb5(%100 : i64)
  ^bb7:  // pred: ^bb5
    %101 = llvm.add %77, %59 : i64
    llvm.br ^bb3(%101 : i64)
  ^bb8:  // pred: ^bb3
    %102 = llvm.add %75, %59 : i64
    llvm.br ^bb1(%102 : i64)
  ^bb9:  // pred: ^bb1
    %103 = llvm.getelementptr %48[40] : (!llvm.ptr) -> !llvm.ptr, f32
    %104 = llvm.ptrtoint %103 : !llvm.ptr to i64
    %105 = llvm.add %104, %61 : i64
    %106 = llvm.call @malloc(%105) : (i64) -> !llvm.ptr
    %107 = llvm.ptrtoint %106 : !llvm.ptr to i64
    %108 = llvm.sub %61, %59 : i64
    %109 = llvm.add %107, %108 : i64
    %110 = llvm.urem %109, %61 : i64
    %111 = llvm.sub %109, %110 : i64
    %112 = llvm.inttoptr %111 : i64 to !llvm.ptr
    llvm.br ^bb10(%58 : i64)
  ^bb10(%113: i64):  // 2 preds: ^bb9, ^bb14
    %114 = llvm.icmp "slt" %113, %59 : i64
    llvm.cond_br %114, ^bb11, ^bb15
  ^bb11:  // pred: ^bb10
    llvm.br ^bb12(%58 : i64)
  ^bb12(%115: i64):  // 2 preds: ^bb11, ^bb13
    %116 = llvm.icmp "slt" %115, %53 : i64
    llvm.cond_br %116, ^bb13, ^bb14
  ^bb13:  // pred: ^bb12
    %117 = llvm.mul %113, %53 : i64
    %118 = llvm.add %117, %115 : i64
    %119 = llvm.getelementptr %112[%118] : (!llvm.ptr, i64) -> !llvm.ptr, f32
    llvm.store %51, %119 : f32, !llvm.ptr
    %120 = llvm.add %115, %59 : i64
    llvm.br ^bb12(%120 : i64)
  ^bb14:  // pred: ^bb12
    %121 = llvm.add %113, %59 : i64
    llvm.br ^bb10(%121 : i64)
  ^bb15:  // pred: ^bb10
    llvm.br ^bb16(%58 : i64)
  ^bb16(%122: i64):  // 2 preds: ^bb15, ^bb23
    %123 = llvm.icmp "slt" %122, %59 : i64
    llvm.cond_br %123, ^bb17, ^bb24
  ^bb17:  // pred: ^bb16
    llvm.br ^bb18(%58 : i64)
  ^bb18(%124: i64):  // 2 preds: ^bb17, ^bb22
    %125 = llvm.icmp "slt" %124, %53 : i64
    llvm.cond_br %125, ^bb19, ^bb23
  ^bb19:  // pred: ^bb18
    llvm.br ^bb20(%58 : i64)
  ^bb20(%126: i64):  // 2 preds: ^bb19, ^bb21
    %127 = llvm.icmp "slt" %126, %54 : i64
    llvm.cond_br %127, ^bb21, ^bb22
  ^bb21:  // pred: ^bb20
    %128 = llvm.mul %122, %49 : i64
    %129 = llvm.mul %124, %54 : i64
    %130 = llvm.add %128, %129 : i64
    %131 = llvm.add %130, %126 : i64
    %132 = llvm.getelementptr %74[%131] : (!llvm.ptr, i64) -> !llvm.ptr, f32
    %133 = llvm.load %132 : !llvm.ptr -> f32
    %134 = llvm.mul %122, %53 : i64
    %135 = llvm.add %134, %124 : i64
    %136 = llvm.getelementptr %112[%135] : (!llvm.ptr, i64) -> !llvm.ptr, f32
    %137 = llvm.load %136 : !llvm.ptr -> f32
    %138 = llvm.fadd %133, %137 : f32
    %139 = llvm.mul %122, %53 : i64
    %140 = llvm.add %139, %124 : i64
    %141 = llvm.getelementptr %112[%140] : (!llvm.ptr, i64) -> !llvm.ptr, f32
    llvm.store %138, %141 : f32, !llvm.ptr
    %142 = llvm.add %126, %59 : i64
    llvm.br ^bb20(%142 : i64)
  ^bb22:  // pred: ^bb20
    %143 = llvm.add %124, %59 : i64
    llvm.br ^bb18(%143 : i64)
  ^bb23:  // pred: ^bb18
    %144 = llvm.add %122, %59 : i64
    llvm.br ^bb16(%144 : i64)
  ^bb24:  // pred: ^bb16
    %145 = llvm.getelementptr %48[40] : (!llvm.ptr) -> !llvm.ptr, f32
    %146 = llvm.ptrtoint %145 : !llvm.ptr to i64
    %147 = llvm.add %146, %61 : i64
    %148 = llvm.call @malloc(%147) : (i64) -> !llvm.ptr
    %149 = llvm.ptrtoint %148 : !llvm.ptr to i64
    %150 = llvm.sub %61, %59 : i64
    %151 = llvm.add %149, %150 : i64
    %152 = llvm.urem %151, %61 : i64
    %153 = llvm.sub %151, %152 : i64
    %154 = llvm.inttoptr %153 : i64 to !llvm.ptr
    llvm.br ^bb25(%58 : i64)
  ^bb25(%155: i64):  // 2 preds: ^bb24, ^bb32
    %156 = llvm.icmp "slt" %155, %59 : i64
    llvm.cond_br %156, ^bb26, ^bb33
  ^bb26:  // pred: ^bb25
    llvm.br ^bb27(%58 : i64)
  ^bb27(%157: i64):  // 2 preds: ^bb26, ^bb31
    %158 = llvm.icmp "slt" %157, %53 : i64
    llvm.cond_br %158, ^bb28, ^bb32
  ^bb28:  // pred: ^bb27
    llvm.br ^bb29(%58 : i64)
  ^bb29(%159: i64):  // 2 preds: ^bb28, ^bb30
    %160 = llvm.icmp "slt" %159, %59 : i64
    llvm.cond_br %160, ^bb30, ^bb31
  ^bb30:  // pred: ^bb29
    %161 = llvm.mul %155, %53 : i64
    %162 = llvm.add %161, %157 : i64
    %163 = llvm.add %162, %159 : i64
    %164 = llvm.getelementptr %112[%163] : (!llvm.ptr, i64) -> !llvm.ptr, f32
    %165 = llvm.load %164 : !llvm.ptr -> f32
    %166 = llvm.fmul %165, %56 : f32
    %167 = llvm.mul %155, %53 : i64
    %168 = llvm.add %167, %157 : i64
    %169 = llvm.add %168, %159 : i64
    %170 = llvm.getelementptr %154[%169] : (!llvm.ptr, i64) -> !llvm.ptr, f32
    llvm.store %166, %170 : f32, !llvm.ptr
    %171 = llvm.add %159, %59 : i64
    llvm.br ^bb29(%171 : i64)
  ^bb31:  // pred: ^bb29
    %172 = llvm.add %157, %59 : i64
    llvm.br ^bb27(%172 : i64)
  ^bb32:  // pred: ^bb27
    %173 = llvm.add %155, %59 : i64
    llvm.br ^bb25(%173 : i64)
  ^bb33:  // pred: ^bb25
    %174 = llvm.getelementptr %48[40] : (!llvm.ptr) -> !llvm.ptr, f32
    %175 = llvm.ptrtoint %174 : !llvm.ptr to i64
    %176 = llvm.add %175, %61 : i64
    %177 = llvm.call @malloc(%176) : (i64) -> !llvm.ptr
    %178 = llvm.ptrtoint %177 : !llvm.ptr to i64
    %179 = llvm.sub %61, %59 : i64
    %180 = llvm.add %178, %179 : i64
    %181 = llvm.urem %180, %61 : i64
    %182 = llvm.sub %180, %181 : i64
    %183 = llvm.inttoptr %182 : i64 to !llvm.ptr
    llvm.br ^bb34(%58 : i64)
  ^bb34(%184: i64):  // 2 preds: ^bb33, ^bb41
    %185 = llvm.icmp "slt" %184, %59 : i64
    llvm.cond_br %185, ^bb35, ^bb42
  ^bb35:  // pred: ^bb34
    llvm.br ^bb36(%58 : i64)
  ^bb36(%186: i64):  // 2 preds: ^bb35, ^bb40
    %187 = llvm.icmp "slt" %186, %53 : i64
    llvm.cond_br %187, ^bb37, ^bb41
  ^bb37:  // pred: ^bb36
    llvm.br ^bb38(%58 : i64)
  ^bb38(%188: i64):  // 2 preds: ^bb37, ^bb39
    %189 = llvm.icmp "slt" %188, %59 : i64
    llvm.cond_br %189, ^bb39, ^bb40
  ^bb39:  // pred: ^bb38
    %190 = llvm.mul %184, %53 : i64
    %191 = llvm.add %190, %186 : i64
    %192 = llvm.add %191, %188 : i64
    %193 = llvm.getelementptr %154[%192] : (!llvm.ptr, i64) -> !llvm.ptr, f32
    %194 = llvm.load %193 : !llvm.ptr -> f32
    %195 = llvm.fadd %194, %57 : f32
    %196 = llvm.mul %184, %53 : i64
    %197 = llvm.add %196, %186 : i64
    %198 = llvm.add %197, %188 : i64
    %199 = llvm.getelementptr %183[%198] : (!llvm.ptr, i64) -> !llvm.ptr, f32
    llvm.store %195, %199 : f32, !llvm.ptr
    %200 = llvm.add %188, %59 : i64
    llvm.br ^bb38(%200 : i64)
  ^bb40:  // pred: ^bb38
    %201 = llvm.add %186, %59 : i64
    llvm.br ^bb36(%201 : i64)
  ^bb41:  // pred: ^bb36
    %202 = llvm.add %184, %59 : i64
    llvm.br ^bb34(%202 : i64)
  ^bb42:  // pred: ^bb34
    %203 = llvm.getelementptr %48[40] : (!llvm.ptr) -> !llvm.ptr, f32
    %204 = llvm.ptrtoint %203 : !llvm.ptr to i64
    %205 = llvm.add %204, %61 : i64
    %206 = llvm.call @malloc(%205) : (i64) -> !llvm.ptr
    %207 = llvm.ptrtoint %206 : !llvm.ptr to i64
    %208 = llvm.sub %61, %59 : i64
    %209 = llvm.add %207, %208 : i64
    %210 = llvm.urem %209, %61 : i64
    %211 = llvm.sub %209, %210 : i64
    %212 = llvm.inttoptr %211 : i64 to !llvm.ptr
    llvm.br ^bb43(%58 : i64)
  ^bb43(%213: i64):  // 2 preds: ^bb42, ^bb50
    %214 = llvm.icmp "slt" %213, %59 : i64
    llvm.cond_br %214, ^bb44, ^bb51
  ^bb44:  // pred: ^bb43
    llvm.br ^bb45(%58 : i64)
  ^bb45(%215: i64):  // 2 preds: ^bb44, ^bb49
    %216 = llvm.icmp "slt" %215, %53 : i64
    llvm.cond_br %216, ^bb46, ^bb50
  ^bb46:  // pred: ^bb45
    llvm.br ^bb47(%58 : i64)
  ^bb47(%217: i64):  // 2 preds: ^bb46, ^bb48
    %218 = llvm.icmp "slt" %217, %59 : i64
    llvm.cond_br %218, ^bb48, ^bb49
  ^bb48:  // pred: ^bb47
    %219 = llvm.mul %213, %53 : i64
    %220 = llvm.add %219, %215 : i64
    %221 = llvm.add %220, %217 : i64
    %222 = llvm.getelementptr %183[%221] : (!llvm.ptr, i64) -> !llvm.ptr, f32
    %223 = llvm.load %222 : !llvm.ptr -> f32
    %224 = llvm.mlir.constant(1.000000e+00 : f32) : f32
    %225 = llvm.intr.sqrt(%223) : (f32) -> f32
    %226 = llvm.fdiv %224, %225 : f32
    %227 = llvm.mul %213, %53 : i64
    %228 = llvm.add %227, %215 : i64
    %229 = llvm.add %228, %217 : i64
    %230 = llvm.getelementptr %212[%229] : (!llvm.ptr, i64) -> !llvm.ptr, f32
    llvm.store %226, %230 : f32, !llvm.ptr
    %231 = llvm.add %217, %59 : i64
    llvm.br ^bb47(%231 : i64)
  ^bb49:  // pred: ^bb47
    %232 = llvm.add %215, %59 : i64
    llvm.br ^bb45(%232 : i64)
  ^bb50:  // pred: ^bb45
    %233 = llvm.add %213, %59 : i64
    llvm.br ^bb43(%233 : i64)
  ^bb51:  // pred: ^bb43
    %234 = llvm.getelementptr %48[163840] : (!llvm.ptr) -> !llvm.ptr, f32
    %235 = llvm.ptrtoint %234 : !llvm.ptr to i64
    %236 = llvm.add %235, %61 : i64
    %237 = llvm.call @malloc(%236) : (i64) -> !llvm.ptr
    %238 = llvm.ptrtoint %237 : !llvm.ptr to i64
    %239 = llvm.sub %61, %59 : i64
    %240 = llvm.add %238, %239 : i64
    %241 = llvm.urem %240, %61 : i64
    %242 = llvm.sub %240, %241 : i64
    %243 = llvm.inttoptr %242 : i64 to !llvm.ptr
    llvm.br ^bb52(%58 : i64)
  ^bb52(%244: i64):  // 2 preds: ^bb51, ^bb59
    %245 = llvm.icmp "slt" %244, %59 : i64
    llvm.cond_br %245, ^bb53, ^bb60
  ^bb53:  // pred: ^bb52
    llvm.br ^bb54(%58 : i64)
  ^bb54(%246: i64):  // 2 preds: ^bb53, ^bb58
    %247 = llvm.icmp "slt" %246, %53 : i64
    llvm.cond_br %247, ^bb55, ^bb59
  ^bb55:  // pred: ^bb54
    llvm.br ^bb56(%58 : i64)
  ^bb56(%248: i64):  // 2 preds: ^bb55, ^bb57
    %249 = llvm.icmp "slt" %248, %54 : i64
    llvm.cond_br %249, ^bb57, ^bb58
  ^bb57:  // pred: ^bb56
    %250 = llvm.extractvalue %39[1] : !llvm.struct<(ptr, ptr, i64, array<3 x i64>, array<3 x i64>)> 
    %251 = llvm.extractvalue %39[2] : !llvm.struct<(ptr, ptr, i64, array<3 x i64>, array<3 x i64>)> 
    %252 = llvm.getelementptr %250[%251] : (!llvm.ptr, i64) -> !llvm.ptr, f32
    %253 = llvm.extractvalue %39[4, 0] : !llvm.struct<(ptr, ptr, i64, array<3 x i64>, array<3 x i64>)> 
    %254 = llvm.mul %244, %253 : i64
    %255 = llvm.extractvalue %39[4, 1] : !llvm.struct<(ptr, ptr, i64, array<3 x i64>, array<3 x i64>)> 
    %256 = llvm.mul %246, %255 : i64
    %257 = llvm.add %254, %256 : i64
    %258 = llvm.extractvalue %39[4, 2] : !llvm.struct<(ptr, ptr, i64, array<3 x i64>, array<3 x i64>)> 
    %259 = llvm.mul %248, %258 : i64
    %260 = llvm.add %257, %259 : i64
    %261 = llvm.getelementptr %252[%260] : (!llvm.ptr, i64) -> !llvm.ptr, f32
    %262 = llvm.load %261 : !llvm.ptr -> f32
    %263 = llvm.mul %244, %53 : i64
    %264 = llvm.add %263, %246 : i64
    %265 = llvm.add %264, %58 : i64
    %266 = llvm.getelementptr %212[%265] : (!llvm.ptr, i64) -> !llvm.ptr, f32
    %267 = llvm.load %266 : !llvm.ptr -> f32
    %268 = llvm.fmul %262, %267 : f32
    %269 = llvm.mul %244, %49 : i64
    %270 = llvm.mul %246, %54 : i64
    %271 = llvm.add %269, %270 : i64
    %272 = llvm.add %271, %248 : i64
    %273 = llvm.getelementptr %243[%272] : (!llvm.ptr, i64) -> !llvm.ptr, f32
    llvm.store %268, %273 : f32, !llvm.ptr
    %274 = llvm.add %248, %59 : i64
    llvm.br ^bb56(%274 : i64)
  ^bb58:  // pred: ^bb56
    %275 = llvm.add %246, %59 : i64
    llvm.br ^bb54(%275 : i64)
  ^bb59:  // pred: ^bb54
    %276 = llvm.add %244, %59 : i64
    llvm.br ^bb52(%276 : i64)
  ^bb60:  // pred: ^bb52
    %277 = llvm.extractvalue %29[1] : !llvm.struct<(ptr, ptr, i64, array<1 x i64>, array<1 x i64>)> 
    %278 = llvm.extractvalue %29[2] : !llvm.struct<(ptr, ptr, i64, array<1 x i64>, array<1 x i64>)> 
    %279 = llvm.extractvalue %29[4, 0] : !llvm.struct<(ptr, ptr, i64, array<1 x i64>, array<1 x i64>)> 
    %280 = llvm.mul %279, %54 overflow<nsw> : i64
    %281 = llvm.mul %279, %54 overflow<nsw> : i64
    %282 = llvm.getelementptr %48[163840] : (!llvm.ptr) -> !llvm.ptr, f32
    %283 = llvm.ptrtoint %282 : !llvm.ptr to i64
    %284 = llvm.add %283, %61 : i64
    %285 = llvm.call @malloc(%284) : (i64) -> !llvm.ptr
    %286 = llvm.ptrtoint %285 : !llvm.ptr to i64
    %287 = llvm.sub %61, %59 : i64
    %288 = llvm.add %286, %287 : i64
    %289 = llvm.urem %288, %61 : i64
    %290 = llvm.sub %288, %289 : i64
    %291 = llvm.inttoptr %290 : i64 to !llvm.ptr
    llvm.br ^bb61(%58 : i64)
  ^bb61(%292: i64):  // 2 preds: ^bb60, ^bb68
    %293 = llvm.icmp "slt" %292, %59 : i64
    llvm.cond_br %293, ^bb62, ^bb69
  ^bb62:  // pred: ^bb61
    llvm.br ^bb63(%58 : i64)
  ^bb63(%294: i64):  // 2 preds: ^bb62, ^bb67
    %295 = llvm.icmp "slt" %294, %53 : i64
    llvm.cond_br %295, ^bb64, ^bb68
  ^bb64:  // pred: ^bb63
    llvm.br ^bb65(%58 : i64)
  ^bb65(%296: i64):  // 2 preds: ^bb64, ^bb66
    %297 = llvm.icmp "slt" %296, %54 : i64
    llvm.cond_br %297, ^bb66, ^bb67
  ^bb66:  // pred: ^bb65
    %298 = llvm.getelementptr %277[%278] : (!llvm.ptr, i64) -> !llvm.ptr, f32
    %299 = llvm.mul %292, %280 : i64
    %300 = llvm.mul %281, %58 : i64
    %301 = llvm.add %299, %300 : i64
    %302 = llvm.mul %296, %279 : i64
    %303 = llvm.add %301, %302 : i64
    %304 = llvm.getelementptr %298[%303] : (!llvm.ptr, i64) -> !llvm.ptr, f32
    %305 = llvm.load %304 : !llvm.ptr -> f32
    %306 = llvm.mul %292, %49 : i64
    %307 = llvm.mul %294, %54 : i64
    %308 = llvm.add %306, %307 : i64
    %309 = llvm.add %308, %296 : i64
    %310 = llvm.getelementptr %243[%309] : (!llvm.ptr, i64) -> !llvm.ptr, f32
    %311 = llvm.load %310 : !llvm.ptr -> f32
    %312 = llvm.fmul %305, %311 : f32
    %313 = llvm.mul %292, %49 : i64
    %314 = llvm.mul %294, %54 : i64
    %315 = llvm.add %313, %314 : i64
    %316 = llvm.add %315, %296 : i64
    %317 = llvm.getelementptr %291[%316] : (!llvm.ptr, i64) -> !llvm.ptr, f32
    llvm.store %312, %317 : f32, !llvm.ptr
    %318 = llvm.add %296, %59 : i64
    llvm.br ^bb65(%318 : i64)
  ^bb67:  // pred: ^bb65
    %319 = llvm.add %294, %59 : i64
    llvm.br ^bb63(%319 : i64)
  ^bb68:  // pred: ^bb63
    %320 = llvm.add %292, %59 : i64
    llvm.br ^bb61(%320 : i64)
  ^bb69:  // pred: ^bb61
    %321 = llvm.getelementptr %48[45088768] : (!llvm.ptr) -> !llvm.ptr, f32
    %322 = llvm.ptrtoint %321 : !llvm.ptr to i64
    %323 = llvm.add %322, %61 : i64
    %324 = llvm.call @malloc(%323) : (i64) -> !llvm.ptr
    %325 = llvm.ptrtoint %324 : !llvm.ptr to i64
    %326 = llvm.sub %61, %59 : i64
    %327 = llvm.add %325, %326 : i64
    %328 = llvm.urem %327, %61 : i64
    %329 = llvm.sub %327, %328 : i64
    %330 = llvm.inttoptr %329 : i64 to !llvm.ptr
    llvm.br ^bb70(%58 : i64)
  ^bb70(%331: i64):  // 2 preds: ^bb69, ^bb74
    %332 = llvm.icmp "slt" %331, %54 : i64
    llvm.cond_br %332, ^bb71, ^bb75
  ^bb71:  // pred: ^bb70
    llvm.br ^bb72(%58 : i64)
  ^bb72(%333: i64):  // 2 preds: ^bb71, ^bb73
    %334 = llvm.icmp "slt" %333, %55 : i64
    llvm.cond_br %334, ^bb73, ^bb74
  ^bb73:  // pred: ^bb72
    %335 = llvm.extractvalue %23[1] : !llvm.struct<(ptr, ptr, i64, array<2 x i64>, array<2 x i64>)> 
    %336 = llvm.extractvalue %23[2] : !llvm.struct<(ptr, ptr, i64, array<2 x i64>, array<2 x i64>)> 
    %337 = llvm.getelementptr %335[%336] : (!llvm.ptr, i64) -> !llvm.ptr, f32
    %338 = llvm.extractvalue %23[4, 0] : !llvm.struct<(ptr, ptr, i64, array<2 x i64>, array<2 x i64>)> 
    %339 = llvm.mul %333, %338 : i64
    %340 = llvm.extractvalue %23[4, 1] : !llvm.struct<(ptr, ptr, i64, array<2 x i64>, array<2 x i64>)> 
    %341 = llvm.mul %331, %340 : i64
    %342 = llvm.add %339, %341 : i64
    %343 = llvm.getelementptr %337[%342] : (!llvm.ptr, i64) -> !llvm.ptr, f32
    %344 = llvm.load %343 : !llvm.ptr -> f32
    %345 = llvm.mul %331, %55 : i64
    %346 = llvm.add %345, %333 : i64
    %347 = llvm.getelementptr %330[%346] : (!llvm.ptr, i64) -> !llvm.ptr, f32
    llvm.store %344, %347 : f32, !llvm.ptr
    %348 = llvm.add %333, %59 : i64
    llvm.br ^bb72(%348 : i64)
  ^bb74:  // pred: ^bb72
    %349 = llvm.add %331, %59 : i64
    llvm.br ^bb70(%349 : i64)
  ^bb75:  // pred: ^bb70
    %350 = llvm.getelementptr %48[440320] : (!llvm.ptr) -> !llvm.ptr, f32
    %351 = llvm.ptrtoint %350 : !llvm.ptr to i64
    %352 = llvm.add %351, %61 : i64
    %353 = llvm.call @malloc(%352) : (i64) -> !llvm.ptr
    %354 = llvm.ptrtoint %353 : !llvm.ptr to i64
    %355 = llvm.sub %61, %59 : i64
    %356 = llvm.add %354, %355 : i64
    %357 = llvm.urem %356, %61 : i64
    %358 = llvm.sub %356, %357 : i64
    %359 = llvm.inttoptr %358 : i64 to !llvm.ptr
    %360 = llvm.mul %59, %53 : i64
    %361 = llvm.mul %360, %55 : i64
    %362 = llvm.getelementptr %48[1] : (!llvm.ptr) -> !llvm.ptr, f32
    %363 = llvm.ptrtoint %362 : !llvm.ptr to i64
    %364 = llvm.mul %361, %363 : i64
    "llvm.intr.memcpy"(%359, %63, %364) <{isVolatile = false}> : (!llvm.ptr, !llvm.ptr, i64) -> ()
    llvm.br ^bb76(%58 : i64)
  ^bb76(%365: i64):  // 2 preds: ^bb75, ^bb83
    %366 = llvm.icmp "slt" %365, %60 : i64
    llvm.cond_br %366, ^bb77, ^bb84
  ^bb77:  // pred: ^bb76
    %367 = llvm.mul %53, %54 : i64
    %368 = llvm.add %367, %54 : i64
    %369 = llvm.getelementptr %291[%368] : (!llvm.ptr, i64) -> !llvm.ptr, f32
    "llvm.intr.prefetch"(%369) <{cache = 1 : i32, hint = 3 : i32, rw = 0 : i32}> : (!llvm.ptr) -> ()
    llvm.br ^bb78(%58 : i64)
  ^bb78(%370: i64):  // 2 preds: ^bb77, ^bb82
    %371 = llvm.icmp "slt" %370, %54 : i64
    llvm.cond_br %371, ^bb79, ^bb83
  ^bb79:  // pred: ^bb78
    %372 = llvm.mul %365, %61 overflow<nsw> : i64
    %373 = llvm.mul %370, %55 : i64
    %374 = llvm.add %373, %372 : i64
    %375 = llvm.getelementptr %330[%374] : (!llvm.ptr, i64) -> !llvm.ptr, f32
    %376 = llvm.load %375 {alignment = 4 : i64} : !llvm.ptr -> vector<64xf32>
    llvm.br ^bb80(%58 : i64)
  ^bb80(%377: i64):  // 2 preds: ^bb79, ^bb81
    %378 = llvm.icmp "slt" %377, %53 : i64
    llvm.cond_br %378, ^bb81, ^bb82
  ^bb81:  // pred: ^bb80
    %379 = llvm.mul %377, %54 : i64
    %380 = llvm.add %379, %370 : i64
    %381 = llvm.getelementptr %291[%380] : (!llvm.ptr, i64) -> !llvm.ptr, f32
    %382 = llvm.load %381 : !llvm.ptr -> f32
    %383 = llvm.insertelement %382, %43[%42 : i32] : vector<64xf32>
    %384 = llvm.shufflevector %383, %43 [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0] : vector<64xf32> 
    %385 = llvm.mul %365, %61 overflow<nsw> : i64
    %386 = llvm.mul %377, %55 : i64
    %387 = llvm.add %386, %385 : i64
    %388 = llvm.getelementptr %359[%387] : (!llvm.ptr, i64) -> !llvm.ptr, f32
    %389 = llvm.load %388 {alignment = 4 : i64} : !llvm.ptr -> vector<64xf32>
    %390 = llvm.intr.fmuladd(%384, %376, %389) : (vector<64xf32>, vector<64xf32>, vector<64xf32>) -> vector<64xf32>
    %391 = llvm.mul %365, %61 overflow<nsw> : i64
    %392 = llvm.mul %377, %55 : i64
    %393 = llvm.add %392, %391 : i64
    %394 = llvm.getelementptr %359[%393] : (!llvm.ptr, i64) -> !llvm.ptr, f32
    llvm.store %390, %394 {alignment = 4 : i64} : vector<64xf32>, !llvm.ptr
    %395 = llvm.add %377, %59 : i64
    llvm.br ^bb80(%395 : i64)
  ^bb82:  // pred: ^bb80
    %396 = llvm.add %370, %59 : i64
    llvm.br ^bb78(%396 : i64)
  ^bb83:  // pred: ^bb78
    %397 = llvm.add %365, %59 : i64
    llvm.br ^bb76(%397 : i64)
  ^bb84:  // pred: ^bb76
    %398 = llvm.getelementptr %48[45088768] : (!llvm.ptr) -> !llvm.ptr, f32
    %399 = llvm.ptrtoint %398 : !llvm.ptr to i64
    %400 = llvm.add %399, %61 : i64
    %401 = llvm.call @malloc(%400) : (i64) -> !llvm.ptr
    %402 = llvm.ptrtoint %401 : !llvm.ptr to i64
    %403 = llvm.sub %61, %59 : i64
    %404 = llvm.add %402, %403 : i64
    %405 = llvm.urem %404, %61 : i64
    %406 = llvm.sub %404, %405 : i64
    %407 = llvm.inttoptr %406 : i64 to !llvm.ptr
    llvm.br ^bb85(%58 : i64)
  ^bb85(%408: i64):  // 2 preds: ^bb84, ^bb89
    %409 = llvm.icmp "slt" %408, %54 : i64
    llvm.cond_br %409, ^bb86, ^bb90
  ^bb86:  // pred: ^bb85
    llvm.br ^bb87(%58 : i64)
  ^bb87(%410: i64):  // 2 preds: ^bb86, ^bb88
    %411 = llvm.icmp "slt" %410, %55 : i64
    llvm.cond_br %411, ^bb88, ^bb89
  ^bb88:  // pred: ^bb87
    %412 = llvm.extractvalue %15[1] : !llvm.struct<(ptr, ptr, i64, array<2 x i64>, array<2 x i64>)> 
    %413 = llvm.extractvalue %15[2] : !llvm.struct<(ptr, ptr, i64, array<2 x i64>, array<2 x i64>)> 
    %414 = llvm.getelementptr %412[%413] : (!llvm.ptr, i64) -> !llvm.ptr, f32
    %415 = llvm.extractvalue %15[4, 0] : !llvm.struct<(ptr, ptr, i64, array<2 x i64>, array<2 x i64>)> 
    %416 = llvm.mul %410, %415 : i64
    %417 = llvm.extractvalue %15[4, 1] : !llvm.struct<(ptr, ptr, i64, array<2 x i64>, array<2 x i64>)> 
    %418 = llvm.mul %408, %417 : i64
    %419 = llvm.add %416, %418 : i64
    %420 = llvm.getelementptr %414[%419] : (!llvm.ptr, i64) -> !llvm.ptr, f32
    %421 = llvm.load %420 : !llvm.ptr -> f32
    %422 = llvm.mul %408, %55 : i64
    %423 = llvm.add %422, %410 : i64
    %424 = llvm.getelementptr %407[%423] : (!llvm.ptr, i64) -> !llvm.ptr, f32
    llvm.store %421, %424 : f32, !llvm.ptr
    %425 = llvm.add %410, %59 : i64
    llvm.br ^bb87(%425 : i64)
  ^bb89:  // pred: ^bb87
    %426 = llvm.add %408, %59 : i64
    llvm.br ^bb85(%426 : i64)
  ^bb90:  // pred: ^bb85
    %427 = llvm.getelementptr %48[440320] : (!llvm.ptr) -> !llvm.ptr, f32
    %428 = llvm.ptrtoint %427 : !llvm.ptr to i64
    %429 = llvm.add %428, %61 : i64
    %430 = llvm.call @malloc(%429) : (i64) -> !llvm.ptr
    %431 = llvm.ptrtoint %430 : !llvm.ptr to i64
    %432 = llvm.sub %61, %59 : i64
    %433 = llvm.add %431, %432 : i64
    %434 = llvm.urem %433, %61 : i64
    %435 = llvm.sub %433, %434 : i64
    %436 = llvm.inttoptr %435 : i64 to !llvm.ptr
    %437 = llvm.mul %59, %53 : i64
    %438 = llvm.mul %437, %55 : i64
    %439 = llvm.getelementptr %48[1] : (!llvm.ptr) -> !llvm.ptr, f32
    %440 = llvm.ptrtoint %439 : !llvm.ptr to i64
    %441 = llvm.mul %438, %440 : i64
    "llvm.intr.memcpy"(%436, %63, %441) <{isVolatile = false}> : (!llvm.ptr, !llvm.ptr, i64) -> ()
    llvm.br ^bb91(%58 : i64)
  ^bb91(%442: i64):  // 2 preds: ^bb90, ^bb98
    %443 = llvm.icmp "slt" %442, %60 : i64
    llvm.cond_br %443, ^bb92, ^bb99
  ^bb92:  // pred: ^bb91
    %444 = llvm.mul %53, %54 : i64
    %445 = llvm.add %444, %54 : i64
    %446 = llvm.getelementptr %291[%445] : (!llvm.ptr, i64) -> !llvm.ptr, f32
    "llvm.intr.prefetch"(%446) <{cache = 1 : i32, hint = 3 : i32, rw = 0 : i32}> : (!llvm.ptr) -> ()
    llvm.br ^bb93(%58 : i64)
  ^bb93(%447: i64):  // 2 preds: ^bb92, ^bb97
    %448 = llvm.icmp "slt" %447, %54 : i64
    llvm.cond_br %448, ^bb94, ^bb98
  ^bb94:  // pred: ^bb93
    %449 = llvm.mul %442, %61 overflow<nsw> : i64
    %450 = llvm.mul %447, %55 : i64
    %451 = llvm.add %450, %449 : i64
    %452 = llvm.getelementptr %407[%451] : (!llvm.ptr, i64) -> !llvm.ptr, f32
    %453 = llvm.load %452 {alignment = 4 : i64} : !llvm.ptr -> vector<64xf32>
    llvm.br ^bb95(%58 : i64)
  ^bb95(%454: i64):  // 2 preds: ^bb94, ^bb96
    %455 = llvm.icmp "slt" %454, %53 : i64
    llvm.cond_br %455, ^bb96, ^bb97
  ^bb96:  // pred: ^bb95
    %456 = llvm.mul %454, %54 : i64
    %457 = llvm.add %456, %447 : i64
    %458 = llvm.getelementptr %291[%457] : (!llvm.ptr, i64) -> !llvm.ptr, f32
    %459 = llvm.load %458 : !llvm.ptr -> f32
    %460 = llvm.insertelement %459, %43[%42 : i32] : vector<64xf32>
    %461 = llvm.shufflevector %460, %43 [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0] : vector<64xf32> 
    %462 = llvm.mul %442, %61 overflow<nsw> : i64
    %463 = llvm.mul %454, %55 : i64
    %464 = llvm.add %463, %462 : i64
    %465 = llvm.getelementptr %436[%464] : (!llvm.ptr, i64) -> !llvm.ptr, f32
    %466 = llvm.load %465 {alignment = 4 : i64} : !llvm.ptr -> vector<64xf32>
    %467 = llvm.intr.fmuladd(%461, %453, %466) : (vector<64xf32>, vector<64xf32>, vector<64xf32>) -> vector<64xf32>
    %468 = llvm.mul %442, %61 overflow<nsw> : i64
    %469 = llvm.mul %454, %55 : i64
    %470 = llvm.add %469, %468 : i64
    %471 = llvm.getelementptr %436[%470] : (!llvm.ptr, i64) -> !llvm.ptr, f32
    llvm.store %467, %471 {alignment = 4 : i64} : vector<64xf32>, !llvm.ptr
    %472 = llvm.add %454, %59 : i64
    llvm.br ^bb95(%472 : i64)
  ^bb97:  // pred: ^bb95
    %473 = llvm.add %447, %59 : i64
    llvm.br ^bb93(%473 : i64)
  ^bb98:  // pred: ^bb93
    %474 = llvm.add %442, %59 : i64
    llvm.br ^bb91(%474 : i64)
  ^bb99:  // pred: ^bb91
    %475 = llvm.getelementptr %48[440320] : (!llvm.ptr) -> !llvm.ptr, f32
    %476 = llvm.ptrtoint %475 : !llvm.ptr to i64
    %477 = llvm.add %476, %61 : i64
    %478 = llvm.call @malloc(%477) : (i64) -> !llvm.ptr
    %479 = llvm.ptrtoint %478 : !llvm.ptr to i64
    %480 = llvm.sub %61, %59 : i64
    %481 = llvm.add %479, %480 : i64
    %482 = llvm.urem %481, %61 : i64
    %483 = llvm.sub %481, %482 : i64
    %484 = llvm.inttoptr %483 : i64 to !llvm.ptr
    llvm.br ^bb100(%58 : i64)
  ^bb100(%485: i64):  // 2 preds: ^bb99, ^bb107
    %486 = llvm.icmp "slt" %485, %59 : i64
    llvm.cond_br %486, ^bb101, ^bb108
  ^bb101:  // pred: ^bb100
    llvm.br ^bb102(%58 : i64)
  ^bb102(%487: i64):  // 2 preds: ^bb101, ^bb106
    %488 = llvm.icmp "slt" %487, %53 : i64
    llvm.cond_br %488, ^bb103, ^bb107
  ^bb103:  // pred: ^bb102
    llvm.br ^bb104(%58 : i64)
  ^bb104(%489: i64):  // 2 preds: ^bb103, ^bb105
    %490 = llvm.icmp "slt" %489, %55 : i64
    llvm.cond_br %490, ^bb105, ^bb106
  ^bb105:  // pred: ^bb104
    %491 = llvm.mul %485, %46 : i64
    %492 = llvm.mul %487, %55 : i64
    %493 = llvm.add %491, %492 : i64
    %494 = llvm.add %493, %489 : i64
    %495 = llvm.getelementptr %359[%494] : (!llvm.ptr, i64) -> !llvm.ptr, f32
    %496 = llvm.load %495 : !llvm.ptr -> f32
    %497 = llvm.fneg %496 : f32
    %498 = llvm.intr.exp(%497) : (f32) -> f32
    %499 = llvm.fadd %498, %50 : f32
    %500 = llvm.fdiv %50, %499 : f32
    %501 = llvm.fmul %496, %500 : f32
    %502 = llvm.mul %485, %46 : i64
    %503 = llvm.mul %487, %55 : i64
    %504 = llvm.add %502, %503 : i64
    %505 = llvm.add %504, %489 : i64
    %506 = llvm.getelementptr %484[%505] : (!llvm.ptr, i64) -> !llvm.ptr, f32
    llvm.store %501, %506 : f32, !llvm.ptr
    %507 = llvm.add %489, %59 : i64
    llvm.br ^bb104(%507 : i64)
  ^bb106:  // pred: ^bb104
    %508 = llvm.add %487, %59 : i64
    llvm.br ^bb102(%508 : i64)
  ^bb107:  // pred: ^bb102
    %509 = llvm.add %485, %59 : i64
    llvm.br ^bb100(%509 : i64)
  ^bb108:  // pred: ^bb100
    llvm.br ^bb109(%58 : i64)
  ^bb109(%510: i64):  // 2 preds: ^bb108, ^bb116
    %511 = llvm.icmp "slt" %510, %59 : i64
    llvm.cond_br %511, ^bb110, ^bb117
  ^bb110:  // pred: ^bb109
    llvm.br ^bb111(%58 : i64)
  ^bb111(%512: i64):  // 2 preds: ^bb110, ^bb115
    %513 = llvm.icmp "slt" %512, %53 : i64
    llvm.cond_br %513, ^bb112, ^bb116
  ^bb112:  // pred: ^bb111
    llvm.br ^bb113(%58 : i64)
  ^bb113(%514: i64):  // 2 preds: ^bb112, ^bb114
    %515 = llvm.icmp "slt" %514, %55 : i64
    llvm.cond_br %515, ^bb114, ^bb115
  ^bb114:  // pred: ^bb113
    %516 = llvm.mul %510, %46 : i64
    %517 = llvm.mul %512, %55 : i64
    %518 = llvm.add %516, %517 : i64
    %519 = llvm.add %518, %514 : i64
    %520 = llvm.getelementptr %484[%519] : (!llvm.ptr, i64) -> !llvm.ptr, f32
    %521 = llvm.load %520 : !llvm.ptr -> f32
    %522 = llvm.mul %510, %46 : i64
    %523 = llvm.mul %512, %55 : i64
    %524 = llvm.add %522, %523 : i64
    %525 = llvm.add %524, %514 : i64
    %526 = llvm.getelementptr %436[%525] : (!llvm.ptr, i64) -> !llvm.ptr, f32
    %527 = llvm.load %526 : !llvm.ptr -> f32
    %528 = llvm.fmul %521, %527 : f32
    %529 = llvm.mul %510, %46 : i64
    %530 = llvm.mul %512, %55 : i64
    %531 = llvm.add %529, %530 : i64
    %532 = llvm.add %531, %514 : i64
    %533 = llvm.getelementptr %484[%532] : (!llvm.ptr, i64) -> !llvm.ptr, f32
    llvm.store %528, %533 : f32, !llvm.ptr
    %534 = llvm.add %514, %59 : i64
    llvm.br ^bb113(%534 : i64)
  ^bb115:  // pred: ^bb113
    %535 = llvm.add %512, %59 : i64
    llvm.br ^bb111(%535 : i64)
  ^bb116:  // pred: ^bb111
    %536 = llvm.add %510, %59 : i64
    llvm.br ^bb109(%536 : i64)
  ^bb117:  // pred: ^bb109
    %537 = llvm.getelementptr %48[45088768] : (!llvm.ptr) -> !llvm.ptr, f32
    %538 = llvm.ptrtoint %537 : !llvm.ptr to i64
    %539 = llvm.add %538, %61 : i64
    %540 = llvm.call @malloc(%539) : (i64) -> !llvm.ptr
    %541 = llvm.ptrtoint %540 : !llvm.ptr to i64
    %542 = llvm.sub %61, %59 : i64
    %543 = llvm.add %541, %542 : i64
    %544 = llvm.urem %543, %61 : i64
    %545 = llvm.sub %543, %544 : i64
    %546 = llvm.inttoptr %545 : i64 to !llvm.ptr
    llvm.br ^bb118(%58 : i64)
  ^bb118(%547: i64):  // 2 preds: ^bb117, ^bb122
    %548 = llvm.icmp "slt" %547, %55 : i64
    llvm.cond_br %548, ^bb119, ^bb123
  ^bb119:  // pred: ^bb118
    llvm.br ^bb120(%58 : i64)
  ^bb120(%549: i64):  // 2 preds: ^bb119, ^bb121
    %550 = llvm.icmp "slt" %549, %54 : i64
    llvm.cond_br %550, ^bb121, ^bb122
  ^bb121:  // pred: ^bb120
    %551 = llvm.extractvalue %7[1] : !llvm.struct<(ptr, ptr, i64, array<2 x i64>, array<2 x i64>)> 
    %552 = llvm.extractvalue %7[2] : !llvm.struct<(ptr, ptr, i64, array<2 x i64>, array<2 x i64>)> 
    %553 = llvm.getelementptr %551[%552] : (!llvm.ptr, i64) -> !llvm.ptr, f32
    %554 = llvm.extractvalue %7[4, 0] : !llvm.struct<(ptr, ptr, i64, array<2 x i64>, array<2 x i64>)> 
    %555 = llvm.mul %549, %554 : i64
    %556 = llvm.extractvalue %7[4, 1] : !llvm.struct<(ptr, ptr, i64, array<2 x i64>, array<2 x i64>)> 
    %557 = llvm.mul %547, %556 : i64
    %558 = llvm.add %555, %557 : i64
    %559 = llvm.getelementptr %553[%558] : (!llvm.ptr, i64) -> !llvm.ptr, f32
    %560 = llvm.load %559 : !llvm.ptr -> f32
    %561 = llvm.mul %547, %54 : i64
    %562 = llvm.add %561, %549 : i64
    %563 = llvm.getelementptr %546[%562] : (!llvm.ptr, i64) -> !llvm.ptr, f32
    llvm.store %560, %563 : f32, !llvm.ptr
    %564 = llvm.add %549, %59 : i64
    llvm.br ^bb120(%564 : i64)
  ^bb122:  // pred: ^bb120
    %565 = llvm.add %547, %59 : i64
    llvm.br ^bb118(%565 : i64)
  ^bb123:  // pred: ^bb118
    %566 = llvm.getelementptr %48[163840] : (!llvm.ptr) -> !llvm.ptr, f32
    %567 = llvm.ptrtoint %566 : !llvm.ptr to i64
    %568 = llvm.add %567, %61 : i64
    %569 = llvm.call @malloc(%568) : (i64) -> !llvm.ptr
    %570 = llvm.ptrtoint %569 : !llvm.ptr to i64
    %571 = llvm.sub %61, %59 : i64
    %572 = llvm.add %570, %571 : i64
    %573 = llvm.urem %572, %61 : i64
    %574 = llvm.sub %572, %573 : i64
    %575 = llvm.inttoptr %574 : i64 to !llvm.ptr
    %576 = llvm.mul %59, %53 : i64
    %577 = llvm.mul %576, %54 : i64
    %578 = llvm.getelementptr %48[1] : (!llvm.ptr) -> !llvm.ptr, f32
    %579 = llvm.ptrtoint %578 : !llvm.ptr to i64
    %580 = llvm.mul %577, %579 : i64
    "llvm.intr.memcpy"(%575, %62, %580) <{isVolatile = false}> : (!llvm.ptr, !llvm.ptr, i64) -> ()
    llvm.br ^bb124(%58 : i64)
  ^bb124(%581: i64):  // 2 preds: ^bb123, ^bb131
    %582 = llvm.icmp "slt" %581, %61 : i64
    llvm.cond_br %582, ^bb125, ^bb132
  ^bb125:  // pred: ^bb124
    %583 = llvm.mul %53, %55 : i64
    %584 = llvm.add %583, %55 : i64
    %585 = llvm.getelementptr %484[%584] : (!llvm.ptr, i64) -> !llvm.ptr, f32
    "llvm.intr.prefetch"(%585) <{cache = 1 : i32, hint = 3 : i32, rw = 0 : i32}> : (!llvm.ptr) -> ()
    llvm.br ^bb126(%58 : i64)
  ^bb126(%586: i64):  // 2 preds: ^bb125, ^bb130
    %587 = llvm.icmp "slt" %586, %55 : i64
    llvm.cond_br %587, ^bb127, ^bb131
  ^bb127:  // pred: ^bb126
    %588 = llvm.mul %581, %61 overflow<nsw> : i64
    %589 = llvm.mul %586, %54 : i64
    %590 = llvm.add %589, %588 : i64
    %591 = llvm.getelementptr %546[%590] : (!llvm.ptr, i64) -> !llvm.ptr, f32
    %592 = llvm.load %591 {alignment = 4 : i64} : !llvm.ptr -> vector<64xf32>
    llvm.br ^bb128(%58 : i64)
  ^bb128(%593: i64):  // 2 preds: ^bb127, ^bb129
    %594 = llvm.icmp "slt" %593, %53 : i64
    llvm.cond_br %594, ^bb129, ^bb130
  ^bb129:  // pred: ^bb128
    %595 = llvm.mul %593, %55 : i64
    %596 = llvm.add %595, %586 : i64
    %597 = llvm.getelementptr %484[%596] : (!llvm.ptr, i64) -> !llvm.ptr, f32
    %598 = llvm.load %597 : !llvm.ptr -> f32
    %599 = llvm.insertelement %598, %43[%42 : i32] : vector<64xf32>
    %600 = llvm.shufflevector %599, %43 [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0] : vector<64xf32> 
    %601 = llvm.mul %581, %61 overflow<nsw> : i64
    %602 = llvm.mul %593, %54 : i64
    %603 = llvm.add %602, %601 : i64
    %604 = llvm.getelementptr %575[%603] : (!llvm.ptr, i64) -> !llvm.ptr, f32
    %605 = llvm.load %604 {alignment = 4 : i64} : !llvm.ptr -> vector<64xf32>
    %606 = llvm.intr.fmuladd(%600, %592, %605) : (vector<64xf32>, vector<64xf32>, vector<64xf32>) -> vector<64xf32>
    %607 = llvm.mul %581, %61 overflow<nsw> : i64
    %608 = llvm.mul %593, %54 : i64
    %609 = llvm.add %608, %607 : i64
    %610 = llvm.getelementptr %575[%609] : (!llvm.ptr, i64) -> !llvm.ptr, f32
    llvm.store %606, %610 {alignment = 4 : i64} : vector<64xf32>, !llvm.ptr
    %611 = llvm.add %593, %59 : i64
    llvm.br ^bb128(%611 : i64)
  ^bb130:  // pred: ^bb128
    %612 = llvm.add %586, %59 : i64
    llvm.br ^bb126(%612 : i64)
  ^bb131:  // pred: ^bb126
    %613 = llvm.add %581, %59 : i64
    llvm.br ^bb124(%613 : i64)
  ^bb132:  // pred: ^bb124
    %614 = llvm.getelementptr %48[163840] : (!llvm.ptr) -> !llvm.ptr, f32
    %615 = llvm.ptrtoint %614 : !llvm.ptr to i64
    %616 = llvm.add %615, %61 : i64
    %617 = llvm.call @malloc(%616) : (i64) -> !llvm.ptr
    %618 = llvm.ptrtoint %617 : !llvm.ptr to i64
    %619 = llvm.sub %61, %59 : i64
    %620 = llvm.add %618, %619 : i64
    %621 = llvm.urem %620, %61 : i64
    %622 = llvm.sub %620, %621 : i64
    %623 = llvm.inttoptr %622 : i64 to !llvm.ptr
    %624 = llvm.insertvalue %617, %44[0] : !llvm.struct<(ptr, ptr, i64, array<3 x i64>, array<3 x i64>)> 
    %625 = llvm.insertvalue %623, %624[1] : !llvm.struct<(ptr, ptr, i64, array<3 x i64>, array<3 x i64>)> 
    %626 = llvm.insertvalue %58, %625[2] : !llvm.struct<(ptr, ptr, i64, array<3 x i64>, array<3 x i64>)> 
    %627 = llvm.insertvalue %59, %626[3, 0] : !llvm.struct<(ptr, ptr, i64, array<3 x i64>, array<3 x i64>)> 
    %628 = llvm.insertvalue %53, %627[3, 1] : !llvm.struct<(ptr, ptr, i64, array<3 x i64>, array<3 x i64>)> 
    %629 = llvm.insertvalue %54, %628[3, 2] : !llvm.struct<(ptr, ptr, i64, array<3 x i64>, array<3 x i64>)> 
    %630 = llvm.insertvalue %49, %629[4, 0] : !llvm.struct<(ptr, ptr, i64, array<3 x i64>, array<3 x i64>)> 
    %631 = llvm.insertvalue %54, %630[4, 1] : !llvm.struct<(ptr, ptr, i64, array<3 x i64>, array<3 x i64>)> 
    %632 = llvm.insertvalue %59, %631[4, 2] : !llvm.struct<(ptr, ptr, i64, array<3 x i64>, array<3 x i64>)> 
    llvm.br ^bb133(%58 : i64)
  ^bb133(%633: i64):  // 2 preds: ^bb132, ^bb140
    %634 = llvm.icmp "slt" %633, %59 : i64
    llvm.cond_br %634, ^bb134, ^bb141
  ^bb134:  // pred: ^bb133
    llvm.br ^bb135(%58 : i64)
  ^bb135(%635: i64):  // 2 preds: ^bb134, ^bb139
    %636 = llvm.icmp "slt" %635, %53 : i64
    llvm.cond_br %636, ^bb136, ^bb140
  ^bb136:  // pred: ^bb135
    llvm.br ^bb137(%58 : i64)
  ^bb137(%637: i64):  // 2 preds: ^bb136, ^bb138
    %638 = llvm.icmp "slt" %637, %54 : i64
    llvm.cond_br %638, ^bb138, ^bb139
  ^bb138:  // pred: ^bb137
    %639 = llvm.extractvalue %39[1] : !llvm.struct<(ptr, ptr, i64, array<3 x i64>, array<3 x i64>)> 
    %640 = llvm.extractvalue %39[2] : !llvm.struct<(ptr, ptr, i64, array<3 x i64>, array<3 x i64>)> 
    %641 = llvm.getelementptr %639[%640] : (!llvm.ptr, i64) -> !llvm.ptr, f32
    %642 = llvm.extractvalue %39[4, 0] : !llvm.struct<(ptr, ptr, i64, array<3 x i64>, array<3 x i64>)> 
    %643 = llvm.mul %633, %642 : i64
    %644 = llvm.extractvalue %39[4, 1] : !llvm.struct<(ptr, ptr, i64, array<3 x i64>, array<3 x i64>)> 
    %645 = llvm.mul %635, %644 : i64
    %646 = llvm.add %643, %645 : i64
    %647 = llvm.extractvalue %39[4, 2] : !llvm.struct<(ptr, ptr, i64, array<3 x i64>, array<3 x i64>)> 
    %648 = llvm.mul %637, %647 : i64
    %649 = llvm.add %646, %648 : i64
    %650 = llvm.getelementptr %641[%649] : (!llvm.ptr, i64) -> !llvm.ptr, f32
    %651 = llvm.load %650 : !llvm.ptr -> f32
    %652 = llvm.mul %633, %49 : i64
    %653 = llvm.mul %635, %54 : i64
    %654 = llvm.add %652, %653 : i64
    %655 = llvm.add %654, %637 : i64
    %656 = llvm.getelementptr %575[%655] : (!llvm.ptr, i64) -> !llvm.ptr, f32
    %657 = llvm.load %656 : !llvm.ptr -> f32
    %658 = llvm.fadd %651, %657 : f32
    %659 = llvm.mul %633, %49 : i64
    %660 = llvm.mul %635, %54 : i64
    %661 = llvm.add %659, %660 : i64
    %662 = llvm.add %661, %637 : i64
    %663 = llvm.getelementptr %623[%662] : (!llvm.ptr, i64) -> !llvm.ptr, f32
    llvm.store %658, %663 : f32, !llvm.ptr
    %664 = llvm.add %637, %59 : i64
    llvm.br ^bb137(%664 : i64)
  ^bb139:  // pred: ^bb137
    %665 = llvm.add %635, %59 : i64
    llvm.br ^bb135(%665 : i64)
  ^bb140:  // pred: ^bb135
    %666 = llvm.add %633, %59 : i64
    llvm.br ^bb133(%666 : i64)
  ^bb141:  // pred: ^bb133
    %667 = llvm.call @rtclock() : () -> f64
    %668 = llvm.fsub %667, %64 : f64
    %669 = llvm.alloca %59 x !llvm.struct<(ptr, ptr, i64, array<3 x i64>, array<3 x i64>)> : (i64) -> !llvm.ptr
    llvm.store %632, %669 : !llvm.struct<(ptr, ptr, i64, array<3 x i64>, array<3 x i64>)>, !llvm.ptr
    %670 = llvm.insertvalue %41, %40[0] : !llvm.struct<(i64, ptr)> 
    %671 = llvm.insertvalue %669, %670[1] : !llvm.struct<(i64, ptr)> 
    llvm.call @printMemrefF32(%41, %669) : (i64, !llvm.ptr) -> ()
    llvm.call @printF64(%668) : (f64) -> ()
    llvm.call @printNewline() : () -> ()
    llvm.return
  }
  llvm.func @main() {
    %0 = llvm.mlir.addressof @__constant_4096x11008xf32 : !llvm.ptr
    %1 = llvm.mlir.addressof @__constant_11008x4096xf32_0 : !llvm.ptr
    %2 = llvm.mlir.undef : !llvm.struct<(ptr, ptr, i64, array<2 x i64>, array<2 x i64>)>
    %3 = llvm.mlir.addressof @__constant_11008x4096xf32 : !llvm.ptr
    %4 = llvm.mlir.constant(11008 : index) : i64
    %5 = llvm.mlir.undef : !llvm.struct<(ptr, ptr, i64, array<1 x i64>, array<1 x i64>)>
    %6 = llvm.mlir.addressof @__constant_4096xf32 : !llvm.ptr
    %7 = llvm.mlir.constant(0 : index) : i64
    %8 = llvm.mlir.undef : !llvm.struct<(ptr, ptr, i64, array<3 x i64>, array<3 x i64>)>
    %9 = llvm.mlir.constant(3735928559 : index) : i64
    %10 = llvm.mlir.addressof @__constant_1x40x4096xf32 : !llvm.ptr
    %11 = llvm.mlir.constant(1 : index) : i64
    %12 = llvm.mlir.constant(40 : index) : i64
    %13 = llvm.mlir.constant(4096 : index) : i64
    %14 = llvm.mlir.constant(163840 : index) : i64
    %15 = llvm.getelementptr %10[0, 0, 0, 0] : (!llvm.ptr) -> !llvm.ptr, !llvm.array<1 x array<40 x array<4096 x f32>>>
    %16 = llvm.inttoptr %9 : i64 to !llvm.ptr
    %17 = llvm.insertvalue %16, %8[0] : !llvm.struct<(ptr, ptr, i64, array<3 x i64>, array<3 x i64>)> 
    %18 = llvm.insertvalue %15, %17[1] : !llvm.struct<(ptr, ptr, i64, array<3 x i64>, array<3 x i64>)> 
    %19 = llvm.insertvalue %7, %18[2] : !llvm.struct<(ptr, ptr, i64, array<3 x i64>, array<3 x i64>)> 
    %20 = llvm.insertvalue %11, %19[3, 0] : !llvm.struct<(ptr, ptr, i64, array<3 x i64>, array<3 x i64>)> 
    %21 = llvm.insertvalue %12, %20[3, 1] : !llvm.struct<(ptr, ptr, i64, array<3 x i64>, array<3 x i64>)> 
    %22 = llvm.insertvalue %13, %21[3, 2] : !llvm.struct<(ptr, ptr, i64, array<3 x i64>, array<3 x i64>)> 
    %23 = llvm.insertvalue %14, %22[4, 0] : !llvm.struct<(ptr, ptr, i64, array<3 x i64>, array<3 x i64>)> 
    %24 = llvm.insertvalue %13, %23[4, 1] : !llvm.struct<(ptr, ptr, i64, array<3 x i64>, array<3 x i64>)> 
    %25 = llvm.insertvalue %11, %24[4, 2] : !llvm.struct<(ptr, ptr, i64, array<3 x i64>, array<3 x i64>)> 
    %26 = llvm.getelementptr %6[0, 0] : (!llvm.ptr) -> !llvm.ptr, !llvm.array<4096 x f32>
    %27 = llvm.inttoptr %9 : i64 to !llvm.ptr
    %28 = llvm.insertvalue %27, %5[0] : !llvm.struct<(ptr, ptr, i64, array<1 x i64>, array<1 x i64>)> 
    %29 = llvm.insertvalue %26, %28[1] : !llvm.struct<(ptr, ptr, i64, array<1 x i64>, array<1 x i64>)> 
    %30 = llvm.insertvalue %7, %29[2] : !llvm.struct<(ptr, ptr, i64, array<1 x i64>, array<1 x i64>)> 
    %31 = llvm.insertvalue %13, %30[3, 0] : !llvm.struct<(ptr, ptr, i64, array<1 x i64>, array<1 x i64>)> 
    %32 = llvm.insertvalue %11, %31[4, 0] : !llvm.struct<(ptr, ptr, i64, array<1 x i64>, array<1 x i64>)> 
    %33 = llvm.getelementptr %3[0, 0, 0] : (!llvm.ptr) -> !llvm.ptr, !llvm.array<11008 x array<4096 x f32>>
    %34 = llvm.inttoptr %9 : i64 to !llvm.ptr
    %35 = llvm.insertvalue %34, %2[0] : !llvm.struct<(ptr, ptr, i64, array<2 x i64>, array<2 x i64>)> 
    %36 = llvm.insertvalue %33, %35[1] : !llvm.struct<(ptr, ptr, i64, array<2 x i64>, array<2 x i64>)> 
    %37 = llvm.insertvalue %7, %36[2] : !llvm.struct<(ptr, ptr, i64, array<2 x i64>, array<2 x i64>)> 
    %38 = llvm.insertvalue %4, %37[3, 0] : !llvm.struct<(ptr, ptr, i64, array<2 x i64>, array<2 x i64>)> 
    %39 = llvm.insertvalue %13, %38[3, 1] : !llvm.struct<(ptr, ptr, i64, array<2 x i64>, array<2 x i64>)> 
    %40 = llvm.insertvalue %13, %39[4, 0] : !llvm.struct<(ptr, ptr, i64, array<2 x i64>, array<2 x i64>)> 
    %41 = llvm.insertvalue %11, %40[4, 1] : !llvm.struct<(ptr, ptr, i64, array<2 x i64>, array<2 x i64>)> 
    %42 = llvm.getelementptr %1[0, 0, 0] : (!llvm.ptr) -> !llvm.ptr, !llvm.array<11008 x array<4096 x f32>>
    %43 = llvm.inttoptr %9 : i64 to !llvm.ptr
    %44 = llvm.insertvalue %43, %2[0] : !llvm.struct<(ptr, ptr, i64, array<2 x i64>, array<2 x i64>)> 
    %45 = llvm.insertvalue %42, %44[1] : !llvm.struct<(ptr, ptr, i64, array<2 x i64>, array<2 x i64>)> 
    %46 = llvm.insertvalue %7, %45[2] : !llvm.struct<(ptr, ptr, i64, array<2 x i64>, array<2 x i64>)> 
    %47 = llvm.insertvalue %4, %46[3, 0] : !llvm.struct<(ptr, ptr, i64, array<2 x i64>, array<2 x i64>)> 
    %48 = llvm.insertvalue %13, %47[3, 1] : !llvm.struct<(ptr, ptr, i64, array<2 x i64>, array<2 x i64>)> 
    %49 = llvm.insertvalue %13, %48[4, 0] : !llvm.struct<(ptr, ptr, i64, array<2 x i64>, array<2 x i64>)> 
    %50 = llvm.insertvalue %11, %49[4, 1] : !llvm.struct<(ptr, ptr, i64, array<2 x i64>, array<2 x i64>)> 
    %51 = llvm.getelementptr %0[0, 0, 0] : (!llvm.ptr) -> !llvm.ptr, !llvm.array<4096 x array<11008 x f32>>
    %52 = llvm.inttoptr %9 : i64 to !llvm.ptr
    %53 = llvm.insertvalue %52, %2[0] : !llvm.struct<(ptr, ptr, i64, array<2 x i64>, array<2 x i64>)> 
    %54 = llvm.insertvalue %51, %53[1] : !llvm.struct<(ptr, ptr, i64, array<2 x i64>, array<2 x i64>)> 
    %55 = llvm.insertvalue %7, %54[2] : !llvm.struct<(ptr, ptr, i64, array<2 x i64>, array<2 x i64>)> 
    %56 = llvm.insertvalue %13, %55[3, 0] : !llvm.struct<(ptr, ptr, i64, array<2 x i64>, array<2 x i64>)> 
    %57 = llvm.insertvalue %4, %56[3, 1] : !llvm.struct<(ptr, ptr, i64, array<2 x i64>, array<2 x i64>)> 
    %58 = llvm.insertvalue %4, %57[4, 0] : !llvm.struct<(ptr, ptr, i64, array<2 x i64>, array<2 x i64>)> 
    %59 = llvm.insertvalue %11, %58[4, 1] : !llvm.struct<(ptr, ptr, i64, array<2 x i64>, array<2 x i64>)> 
    llvm.call @kernel_optimized(%16, %15, %7, %11, %12, %13, %14, %13, %11, %27, %26, %7, %13, %11, %34, %33, %7, %4, %13, %13, %11, %43, %42, %7, %4, %13, %13, %11, %52, %51, %7, %13, %4, %4, %11) : (!llvm.ptr, !llvm.ptr, i64, i64, i64, i64, i64, i64, i64, !llvm.ptr, !llvm.ptr, i64, i64, i64, !llvm.ptr, !llvm.ptr, i64, i64, i64, i64, i64, !llvm.ptr, !llvm.ptr, i64, i64, i64, i64, i64, !llvm.ptr, !llvm.ptr, i64, i64, i64, i64, i64) -> ()
    llvm.return
  }
}

