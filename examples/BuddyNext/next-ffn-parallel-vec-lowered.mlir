#map = affine_map<()[s0] -> (s0 * 4096)>
module {
  memref.global "private" constant @__constant_4096x11008xf32 : memref<4096x11008xf32> = dense<6.000000e+00> {alignment = 64 : i64}
  memref.global "private" constant @__constant_11008x4096xf32_0 : memref<11008x4096xf32> = dense<5.000000e+00> {alignment = 64 : i64}
  memref.global "private" constant @__constant_11008x4096xf32 : memref<11008x4096xf32> = dense<4.000000e+00> {alignment = 64 : i64}
  memref.global "private" constant @__constant_4096xf32 : memref<4096xf32> = dense<3.000000e+00> {alignment = 64 : i64}
  memref.global "private" constant @__constant_1x40x4096xf32 : memref<1x40x4096xf32> = dense<2.000000e+00> {alignment = 64 : i64}
  memref.global "private" constant @__constant_1x1x1xf32 : memref<1x1x1xf32> = dense<2.44140625E-4> {alignment = 64 : i64}
  memref.global "private" constant @__constant_1x40x1xf32 : memref<1x40x1xf32> = dense<9.99999974E-6> {alignment = 64 : i64}
  memref.global "private" constant @__constant_40x11008xf32 : memref<40x11008xf32> = dense<0.000000e+00> {alignment = 64 : i64}
  memref.global "private" constant @__constant_40x4096xf32 : memref<40x4096xf32> = dense<0.000000e+00> {alignment = 64 : i64}
  func.func private @rtclock() -> f64
  func.func private @printMemrefF32(memref<*xf32>)
  func.func @kernel(%arg0: memref<1x40x4096xf32, strided<[?, ?, ?], offset: ?>>, %arg1: memref<4096xf32, strided<[?], offset: ?>>, %arg2: memref<11008x4096xf32, strided<[?, ?], offset: ?>>, %arg3: memref<11008x4096xf32, strided<[?, ?], offset: ?>>, %arg4: memref<4096x11008xf32, strided<[?, ?], offset: ?>>) {
    %c0 = arith.constant 0 : index
    %cst = arith.constant 9.99999974E-6 : f32
    %cst_0 = arith.constant 2.44140625E-4 : f32
    %c11008 = arith.constant 11008 : index
    %c4096 = arith.constant 4096 : index
    %c40 = arith.constant 40 : index
    %c2_i32 = arith.constant 2 : i32
    %cst_1 = arith.constant 0.000000e+00 : f32
    %cst_2 = arith.constant 1.000000e+00 : f32
    %0 = memref.get_global @__constant_40x4096xf32 : memref<40x4096xf32>
    %1 = memref.get_global @__constant_40x11008xf32 : memref<40x11008xf32>
    %2 = call @rtclock() : () -> f64
    %alloc = memref.alloc() {alignment = 64 : i64} : memref<1x40x4096xf32>
    affine.for %arg5 = 0 to 1 {
      affine.for %arg6 = 0 to 40 {
        affine.for %arg7 = 0 to 4096 {
          %7 = affine.load %arg0[%arg5, %arg6, %arg7] : memref<1x40x4096xf32, strided<[?, ?, ?], offset: ?>>
          %8 = math.fpowi %7, %c2_i32 : f32, i32
          affine.store %8, %alloc[%arg5, %arg6, %arg7] : memref<1x40x4096xf32>
        }
      }
    }
    %alloc_3 = memref.alloc() {alignment = 64 : i64} : memref<1x40xf32>
    affine.for %arg5 = 0 to 1 {
      affine.for %arg6 = 0 to 40 {
        affine.store %cst_1, %alloc_3[%arg5, %arg6] : memref<1x40xf32>
      }
    }
    affine.for %arg5 = 0 to 1 {
      affine.for %arg6 = 0 to 40 {
        affine.for %arg7 = 0 to 4096 {
          %7 = affine.load %alloc[%arg5, %arg6, %arg7] : memref<1x40x4096xf32>
          %8 = affine.load %alloc_3[%arg5, %arg6] : memref<1x40xf32>
          %9 = arith.addf %7, %8 : f32
          affine.store %9, %alloc_3[%arg5, %arg6] : memref<1x40xf32>
        }
      }
    }
    %reinterpret_cast = memref.reinterpret_cast %alloc_3 to offset: [0], sizes: [1, 40, 1], strides: [40, 1, 1] : memref<1x40xf32> to memref<1x40x1xf32>
    %alloc_4 = memref.alloc() {alignment = 64 : i64} : memref<1x40x1xf32>
    affine.for %arg5 = 0 to 1 {
      affine.for %arg6 = 0 to 40 {
        affine.for %arg7 = 0 to 1 {
          %7 = affine.load %reinterpret_cast[%arg5, %arg6, %arg7] : memref<1x40x1xf32>
          %8 = arith.mulf %7, %cst_0 : f32
          affine.store %8, %alloc_4[%arg5, %arg6, %arg7] : memref<1x40x1xf32>
        }
      }
    }
    %alloc_5 = memref.alloc() {alignment = 64 : i64} : memref<1x40x1xf32>
    affine.for %arg5 = 0 to 1 {
      affine.for %arg6 = 0 to 40 {
        affine.for %arg7 = 0 to 1 {
          %7 = affine.load %alloc_4[%arg5, %arg6, %arg7] : memref<1x40x1xf32>
          %8 = arith.addf %7, %cst : f32
          affine.store %8, %alloc_5[%arg5, %arg6, %arg7] : memref<1x40x1xf32>
        }
      }
    }
    %alloc_6 = memref.alloc() {alignment = 64 : i64} : memref<1x40x1xf32>
    affine.for %arg5 = 0 to 1 {
      affine.for %arg6 = 0 to 40 {
        affine.for %arg7 = 0 to 1 {
          %7 = affine.load %alloc_5[%arg5, %arg6, %arg7] : memref<1x40x1xf32>
          %8 = math.rsqrt %7 : f32
          affine.store %8, %alloc_6[%arg5, %arg6, %arg7] : memref<1x40x1xf32>
        }
      }
    }
    %alloc_7 = memref.alloc() {alignment = 64 : i64} : memref<1x40x4096xf32>
    affine.for %arg5 = 0 to 1 {
      affine.for %arg6 = 0 to 40 {
        affine.for %arg7 = 0 to 4096 {
          %7 = affine.load %arg0[%arg5, %arg6, %arg7] : memref<1x40x4096xf32, strided<[?, ?, ?], offset: ?>>
          %8 = affine.load %alloc_6[%arg5, %arg6, %c0] : memref<1x40x1xf32>
          %9 = arith.mulf %7, %8 : f32
          affine.store %9, %alloc_7[%arg5, %arg6, %arg7] : memref<1x40x4096xf32>
        }
      }
    }
    %base_buffer, %offset, %sizes, %strides = memref.extract_strided_metadata %arg1 : memref<4096xf32, strided<[?], offset: ?>> -> memref<f32>, index, index, index
    %3 = affine.apply #map()[%strides]
    %4 = affine.apply #map()[%strides]
    %reinterpret_cast_8 = memref.reinterpret_cast %base_buffer to offset: [%offset], sizes: [1, 1, 4096], strides: [%3, %4, %strides] : memref<f32> to memref<1x1x4096xf32, strided<[?, ?, ?], offset: ?>>
    %alloc_9 = memref.alloc() {alignment = 64 : i64} : memref<1x40x4096xf32>
    affine.for %arg5 = 0 to 1 {
      affine.for %arg6 = 0 to 40 {
        affine.for %arg7 = 0 to 4096 {
          %7 = affine.load %reinterpret_cast_8[%arg5, %c0, %arg7] : memref<1x1x4096xf32, strided<[?, ?, ?], offset: ?>>
          %8 = affine.load %alloc_7[%arg5, %arg6, %arg7] : memref<1x40x4096xf32>
          %9 = arith.mulf %7, %8 : f32
          affine.store %9, %alloc_9[%arg5, %arg6, %arg7] : memref<1x40x4096xf32>
        }
      }
    }
    %alloc_10 = memref.alloc() {alignment = 64 : i64} : memref<4096x11008xf32>
    affine.for %arg5 = 0 to 4096 {
      affine.for %arg6 = 0 to 11008 {
        %7 = affine.load %arg2[%arg6, %arg5] : memref<11008x4096xf32, strided<[?, ?], offset: ?>>
        affine.store %7, %alloc_10[%arg5, %arg6] : memref<4096x11008xf32>
      }
    }
    %reinterpret_cast_11 = memref.reinterpret_cast %alloc_9 to offset: [0], sizes: [40, 4096], strides: [4096, 1] : memref<1x40x4096xf32> to memref<40x4096xf32>
    %alloc_12 = memref.alloc() {alignment = 64 : i64} : memref<40x11008xf32>
    memref.copy %1, %alloc_12 : memref<40x11008xf32> to memref<40x11008xf32>
    affine.parallel (%arg5) = (0) to (172) {
      affine.prefetch %reinterpret_cast_11[%c40, %c4096], read, locality<3>, data : memref<40x4096xf32>
      affine.for %arg6 = 0 to 4096 {
        %7 = affine.vector_load %alloc_10[%arg6, %arg5 * 64] : memref<4096x11008xf32>, vector<64xf32>
        affine.for %arg7 = 0 to 40 {
          %8 = memref.load %reinterpret_cast_11[%arg7, %arg6] : memref<40x4096xf32>
          %9 = vector.broadcast %8 : f32 to vector<64xf32>
          %10 = affine.vector_load %alloc_12[%arg7, %arg5 * 64] : memref<40x11008xf32>, vector<64xf32>
          %11 = vector.fma %9, %7, %10 : vector<64xf32>
          affine.vector_store %11, %alloc_12[%arg7, %arg5 * 64] : memref<40x11008xf32>, vector<64xf32>
        }
      }
    }
    %reinterpret_cast_13 = memref.reinterpret_cast %alloc_12 to offset: [0], sizes: [1, 40, 11008], strides: [440320, 11008, 1] : memref<40x11008xf32> to memref<1x40x11008xf32>
    %alloc_14 = memref.alloc() {alignment = 64 : i64} : memref<1x40x11008xf32>
    affine.for %arg5 = 0 to 1 {
      affine.for %arg6 = 0 to 40 {
        affine.for %arg7 = 0 to 11008 {
          %7 = affine.load %reinterpret_cast_13[%arg5, %arg6, %arg7] : memref<1x40x11008xf32>
          %8 = arith.negf %7 : f32
          %9 = math.exp %8 : f32
          %10 = arith.addf %9, %cst_2 : f32
          %11 = arith.divf %cst_2, %10 : f32
          affine.store %11, %alloc_14[%arg5, %arg6, %arg7] : memref<1x40x11008xf32>
        }
      }
    }
    %alloc_15 = memref.alloc() {alignment = 64 : i64} : memref<1x40x11008xf32>
    affine.for %arg5 = 0 to 1 {
      affine.for %arg6 = 0 to 40 {
        affine.for %arg7 = 0 to 11008 {
          %7 = affine.load %reinterpret_cast_13[%arg5, %arg6, %arg7] : memref<1x40x11008xf32>
          %8 = affine.load %alloc_14[%arg5, %arg6, %arg7] : memref<1x40x11008xf32>
          %9 = arith.mulf %7, %8 : f32
          affine.store %9, %alloc_15[%arg5, %arg6, %arg7] : memref<1x40x11008xf32>
        }
      }
    }
    %alloc_16 = memref.alloc() {alignment = 64 : i64} : memref<4096x11008xf32>
    affine.for %arg5 = 0 to 4096 {
      affine.for %arg6 = 0 to 11008 {
        %7 = affine.load %arg3[%arg6, %arg5] : memref<11008x4096xf32, strided<[?, ?], offset: ?>>
        affine.store %7, %alloc_16[%arg5, %arg6] : memref<4096x11008xf32>
      }
    }
    %reinterpret_cast_17 = memref.reinterpret_cast %alloc_9 to offset: [0], sizes: [40, 4096], strides: [4096, 1] : memref<1x40x4096xf32> to memref<40x4096xf32>
    %alloc_18 = memref.alloc() {alignment = 64 : i64} : memref<40x11008xf32>
    memref.copy %1, %alloc_18 : memref<40x11008xf32> to memref<40x11008xf32>
    affine.parallel (%arg5) = (0) to (172) {
      affine.prefetch %reinterpret_cast_17[%c40, %c4096], read, locality<3>, data : memref<40x4096xf32>
      affine.for %arg6 = 0 to 4096 {
        %7 = affine.vector_load %alloc_16[%arg6, %arg5 * 64] : memref<4096x11008xf32>, vector<64xf32>
        affine.for %arg7 = 0 to 40 {
          %8 = memref.load %reinterpret_cast_17[%arg7, %arg6] : memref<40x4096xf32>
          %9 = vector.broadcast %8 : f32 to vector<64xf32>
          %10 = affine.vector_load %alloc_18[%arg7, %arg5 * 64] : memref<40x11008xf32>, vector<64xf32>
          %11 = vector.fma %9, %7, %10 : vector<64xf32>
          affine.vector_store %11, %alloc_18[%arg7, %arg5 * 64] : memref<40x11008xf32>, vector<64xf32>
        }
      }
    }
    %reinterpret_cast_19 = memref.reinterpret_cast %alloc_18 to offset: [0], sizes: [1, 40, 11008], strides: [440320, 11008, 1] : memref<40x11008xf32> to memref<1x40x11008xf32>
    %alloc_20 = memref.alloc() {alignment = 64 : i64} : memref<1x40x11008xf32>
    affine.for %arg5 = 0 to 1 {
      affine.for %arg6 = 0 to 40 {
        affine.for %arg7 = 0 to 11008 {
          %7 = affine.load %alloc_15[%arg5, %arg6, %arg7] : memref<1x40x11008xf32>
          %8 = affine.load %reinterpret_cast_19[%arg5, %arg6, %arg7] : memref<1x40x11008xf32>
          %9 = arith.mulf %7, %8 : f32
          affine.store %9, %alloc_20[%arg5, %arg6, %arg7] : memref<1x40x11008xf32>
        }
      }
    }
    %alloc_21 = memref.alloc() {alignment = 64 : i64} : memref<11008x4096xf32>
    affine.for %arg5 = 0 to 11008 {
      affine.for %arg6 = 0 to 4096 {
        %7 = affine.load %arg4[%arg6, %arg5] : memref<4096x11008xf32, strided<[?, ?], offset: ?>>
        affine.store %7, %alloc_21[%arg5, %arg6] : memref<11008x4096xf32>
      }
    }
    %reinterpret_cast_22 = memref.reinterpret_cast %alloc_20 to offset: [0], sizes: [40, 11008], strides: [11008, 1] : memref<1x40x11008xf32> to memref<40x11008xf32>
    %alloc_23 = memref.alloc() {alignment = 64 : i64} : memref<40x4096xf32>
    memref.copy %0, %alloc_23 : memref<40x4096xf32> to memref<40x4096xf32>
    affine.parallel (%arg5) = (0) to (64) {
      affine.prefetch %reinterpret_cast_22[%c40, %c11008], read, locality<3>, data : memref<40x11008xf32>
      affine.for %arg6 = 0 to 11008 {
        %7 = affine.vector_load %alloc_21[%arg6, %arg5 * 64] : memref<11008x4096xf32>, vector<64xf32>
        affine.for %arg7 = 0 to 40 {
          %8 = memref.load %reinterpret_cast_22[%arg7, %arg6] : memref<40x11008xf32>
          %9 = vector.broadcast %8 : f32 to vector<64xf32>
          %10 = affine.vector_load %alloc_23[%arg7, %arg5 * 64] : memref<40x4096xf32>, vector<64xf32>
          %11 = vector.fma %9, %7, %10 : vector<64xf32>
          affine.vector_store %11, %alloc_23[%arg7, %arg5 * 64] : memref<40x4096xf32>, vector<64xf32>
        }
      }
    }
    %reinterpret_cast_24 = memref.reinterpret_cast %alloc_23 to offset: [0], sizes: [1, 40, 4096], strides: [163840, 4096, 1] : memref<40x4096xf32> to memref<1x40x4096xf32>
    %alloc_25 = memref.alloc() {alignment = 64 : i64} : memref<1x40x4096xf32>
    affine.for %arg5 = 0 to 1 {
      affine.for %arg6 = 0 to 40 {
        affine.for %arg7 = 0 to 4096 {
          %7 = affine.load %arg0[%arg5, %arg6, %arg7] : memref<1x40x4096xf32, strided<[?, ?, ?], offset: ?>>
          %8 = affine.load %reinterpret_cast_24[%arg5, %arg6, %arg7] : memref<1x40x4096xf32>
          %9 = arith.addf %7, %8 : f32
          affine.store %9, %alloc_25[%arg5, %arg6, %arg7] : memref<1x40x4096xf32>
        }
      }
    }
    %5 = call @rtclock() : () -> f64
    %6 = arith.subf %5, %2 : f64
    %cast = memref.cast %alloc_25 : memref<1x40x4096xf32> to memref<*xf32>
    call @printMemrefF32(%cast) : (memref<*xf32>) -> ()
    vector.print %6 : f64
    return
  }
  func.func @main() {
    %0 = memref.get_global @__constant_1x40x4096xf32 : memref<1x40x4096xf32>
    %1 = memref.get_global @__constant_4096xf32 : memref<4096xf32>
    %2 = memref.get_global @__constant_11008x4096xf32 : memref<11008x4096xf32>
    %3 = memref.get_global @__constant_11008x4096xf32_0 : memref<11008x4096xf32>
    %4 = memref.get_global @__constant_4096x11008xf32 : memref<4096x11008xf32>
    %cast = memref.cast %0 : memref<1x40x4096xf32> to memref<1x40x4096xf32, strided<[?, ?, ?], offset: ?>>
    %cast_0 = memref.cast %1 : memref<4096xf32> to memref<4096xf32, strided<[?], offset: ?>>
    %cast_1 = memref.cast %2 : memref<11008x4096xf32> to memref<11008x4096xf32, strided<[?, ?], offset: ?>>
    %cast_2 = memref.cast %3 : memref<11008x4096xf32> to memref<11008x4096xf32, strided<[?, ?], offset: ?>>
    %cast_3 = memref.cast %4 : memref<4096x11008xf32> to memref<4096x11008xf32, strided<[?, ?], offset: ?>>
    call @kernel(%cast, %cast_0, %cast_1, %cast_2, %cast_3) : (memref<1x40x4096xf32, strided<[?, ?, ?], offset: ?>>, memref<4096xf32, strided<[?], offset: ?>>, memref<11008x4096xf32, strided<[?, ?], offset: ?>>, memref<11008x4096xf32, strided<[?, ?], offset: ?>>, memref<4096x11008xf32, strided<[?, ?], offset: ?>>) -> ()
    return
  }
}

