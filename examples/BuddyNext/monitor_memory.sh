#!/bin/bash

# 内存监控脚本
# 使用方法: ./monitor_memory.sh "make next-ffn-optimized-run"

if [ $# -eq 0 ]; then
    echo "使用方法: $0 \"命令\""
    echo "例如: $0 \"make next-ffn-optimized-run\""
    exit 1
fi

COMMAND="$1"
LOG_FILE="memory_usage.log"
INTERVAL=0.1  # 监控间隔（秒）

echo "开始监控命令: $COMMAND"
echo "日志文件: $LOG_FILE"
echo "监控间隔: ${INTERVAL}s"
echo "========================================="

# 清空日志文件
> $LOG_FILE

# 在后台运行目标命令
$COMMAND &
TARGET_PID=$!

echo "目标进程 PID: $TARGET_PID"

# 记录开始时间
START_TIME=$(date +%s.%N)
echo "时间(s),RSS(MB),VSZ(MB),CPU(%)" > $LOG_FILE

# 监控内存使用
MAX_RSS=0
while kill -0 $TARGET_PID 2>/dev/null; do
    # 获取当前时间
    CURRENT_TIME=$(date +%s.%N)
    ELAPSED=$(echo "$CURRENT_TIME - $START_TIME" | bc)
    
    # 获取内存信息 (RSS: 物理内存, VSZ: 虚拟内存)
    if ps -p $TARGET_PID -o pid,rss,vsz,pcpu --no-headers > /dev/null 2>&1; then
        MEMORY_INFO=$(ps -p $TARGET_PID -o rss,vsz,pcpu --no-headers)
        RSS_KB=$(echo $MEMORY_INFO | awk '{print $1}')
        VSZ_KB=$(echo $MEMORY_INFO | awk '{print $2}')
        CPU_PERCENT=$(echo $MEMORY_INFO | awk '{print $3}')
        
        # 转换为 MB
        RSS_MB=$(echo "scale=2; $RSS_KB / 1024" | bc)
        VSZ_MB=$(echo "scale=2; $VSZ_KB / 1024" | bc)
        
        # 记录到日志
        echo "$ELAPSED,$RSS_MB,$VSZ_MB,$CPU_PERCENT" >> $LOG_FILE
        
        # 更新最大内存使用
        if (( $(echo "$RSS_KB > $MAX_RSS" | bc -l) )); then
            MAX_RSS=$RSS_KB
        fi
        
        # 实时显示
        printf "\r时间: %6.2fs | RSS: %8.2f MB | VSZ: %8.2f MB | CPU: %6.2f%% | 峰值: %8.2f MB" \
               $ELAPSED $RSS_MB $VSZ_MB $CPU_PERCENT $(echo "scale=2; $MAX_RSS / 1024" | bc)
    fi
    
    sleep $INTERVAL
done

echo ""
echo "========================================="
echo "监控完成!"
echo "最大内存使用量: $(echo "scale=2; $MAX_RSS / 1024" | bc) MB"
echo "详细日志保存在: $LOG_FILE"

# 生成简单的统计报告
if [ -f $LOG_FILE ]; then
    echo ""
    echo "内存使用统计:"
    echo "平均 RSS: $(tail -n +2 $LOG_FILE | awk -F, '{sum+=$2; count++} END {printf "%.2f MB\n", sum/count}')"
    echo "最大 RSS: $(tail -n +2 $LOG_FILE | awk -F, 'BEGIN{max=0} {if($2>max) max=$2} END {printf "%.2f MB\n", max}')"
    echo "平均 CPU: $(tail -n +2 $LOG_FILE | awk -F, '{sum+=$4; count++} END {printf "%.2f%%\n", sum/count}')"
fi
